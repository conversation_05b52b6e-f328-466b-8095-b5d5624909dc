// Test the fixed document processing pipeline
const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');

const BASE_URL = 'http://localhost:3000/api';
const AUTH_TOKEN = 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************.kNQXDoCw1m0dfwK6cPmZvDErewqaOAfj2Hrvdwv4yK8';

async function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

async function testFixedDocumentProcessing() {
  console.log('🔧 Testing Fixed Document Processing Pipeline...\n');

  let documentId = null;
  let sessionId = null;
  const testFilePath = './test-contract-fixed.txt';

  try {
    // Create a comprehensive test document
    const testContent = `
COMPREHENSIVE SERVICE AGREEMENT

This Service Agreement ("Agreement") is entered into on January 15, 2024, between TechCorp Solutions, a Delaware corporation ("Provider"), and Global Industries Inc., a New York corporation ("Client").

ARTICLE 1: SERVICES AND DELIVERABLES
Provider agrees to provide the following comprehensive services to Client:
1. Software development and maintenance services
2. Technical consulting and system architecture design
3. System integration and deployment services
4. Training, documentation, and ongoing support
5. Quality assurance and testing services

ARTICLE 2: TERM AND TERMINATION
This Agreement shall commence on February 1, 2024, and shall continue for a period of twelve (12) months, unless terminated earlier in accordance with the provisions herein. Either party may terminate this Agreement with thirty (30) days written notice to the other party. Upon termination, all work product shall be delivered to Client.

ARTICLE 3: COMPENSATION AND PAYMENT TERMS
Client agrees to pay Provider a total fee of $150,000 for the services described herein, payable in monthly installments of $12,500. Payment terms are Net 30 days from invoice date. Late payments will incur a 1.5% monthly service charge. All expenses must be pre-approved in writing.

ARTICLE 4: CONFIDENTIALITY AND NON-DISCLOSURE
Both parties acknowledge that they may have access to confidential information during the performance of this Agreement. Each party agrees to maintain the confidentiality of such information and not disclose it to third parties without prior written consent. This obligation shall survive termination of this Agreement.

ARTICLE 5: INTELLECTUAL PROPERTY RIGHTS
All intellectual property developed under this Agreement shall be owned by Client, except for Provider's pre-existing intellectual property and general methodologies. Provider grants Client a perpetual, non-exclusive license to use any pre-existing intellectual property incorporated into the deliverables.

ARTICLE 6: LIABILITY AND INDEMNIFICATION
Provider's liability under this Agreement shall not exceed the total amount paid by Client under this Agreement. Each party agrees to indemnify the other against claims arising from their negligent acts or omissions. Neither party shall be liable for consequential or indirect damages.

ARTICLE 7: FORCE MAJEURE
Neither party shall be liable for any failure or delay in performance under this Agreement which is due to fire, flood, earthquake, elements of nature, acts of God, acts of war, terrorism, riots, civil disorders, rebellions or revolutions, or any other cause beyond the reasonable control of such party.

ARTICLE 8: GOVERNING LAW AND DISPUTE RESOLUTION
This Agreement shall be governed by the laws of the State of Delaware, without regard to conflict of law principles. Any disputes arising under this Agreement shall be resolved through binding arbitration in accordance with the rules of the American Arbitration Association.

ARTICLE 9: ENTIRE AGREEMENT AND MODIFICATIONS
This Agreement constitutes the entire agreement between the parties and supersedes all prior negotiations, representations, or agreements relating to the subject matter herein. This Agreement may only be modified in writing signed by both parties.

IN WITNESS WHEREOF, the parties have executed this Agreement as of the date first written above.

TECHCORP SOLUTIONS                    GLOBAL INDUSTRIES INC.

_________________________            _________________________
John Smith, CEO                       Sarah Johnson, CTO
Date: January 15, 2024               Date: January 15, 2024

EXHIBIT A: DETAILED SCOPE OF WORK
[Detailed technical specifications and deliverables would be attached here]

EXHIBIT B: PAYMENT SCHEDULE
[Detailed payment milestones and schedule would be attached here]
    `.trim();

    fs.writeFileSync(testFilePath, testContent);
    console.log(`📄 Created comprehensive test document: ${testContent.length} characters`);

    // Step 1: Upload Document
    console.log('\n📤 Step 1: Uploading document...');
    const formData = new FormData();
    formData.append('file', fs.createReadStream(testFilePath));
    formData.append('title', 'Comprehensive Service Agreement');
    formData.append('author', 'Legal Department');

    const uploadResponse = await axios.post(`${BASE_URL}/documents/upload`, formData, {
      headers: {
        ...formData.getHeaders(),
        'Authorization': AUTH_TOKEN
      },
      timeout: 30000
    });

    documentId = uploadResponse.data.document.id;
    console.log('✅ Document uploaded successfully');
    console.log(`   Document ID: ${documentId}`);
    console.log(`   Initial Status: ${uploadResponse.data.document.processingStatus}`);

    // Step 2: Wait a moment for automatic processing
    console.log('\n⏳ Step 2: Waiting for automatic processing...');
    await sleep(5000);

    // Check if automatic processing worked
    let processingComplete = false;
    for (let attempt = 1; attempt <= 5; attempt++) {
      try {
        const statusResponse = await axios.get(`${BASE_URL}/documents/${documentId}/processing-status`, {
          headers: { 'Authorization': AUTH_TOKEN }
        });
        
        console.log(`   Attempt ${attempt}: Status = ${statusResponse.data.status}`);
        
        if (statusResponse.data.status === 'completed' || statusResponse.data.status === 'analyzed') {
          processingComplete = true;
          console.log('✅ Automatic processing completed successfully!');
          break;
        } else if (statusResponse.data.status === 'failed') {
          console.log('❌ Automatic processing failed, will try manual trigger');
          break;
        }
      } catch (error) {
        console.log(`   Status check ${attempt} failed: ${error.message}`);
      }
      
      await sleep(3000);
    }

    // Step 3: Manual processing trigger if needed
    if (!processingComplete) {
      console.log('\n🔄 Step 3: Manually triggering processing...');
      try {
        const triggerResponse = await axios.post(`${BASE_URL}/documents/${documentId}/process`, {}, {
          headers: { 'Authorization': AUTH_TOKEN }
        });
        
        console.log('✅ Manual processing triggered successfully');
        console.log(`   Job ID: ${triggerResponse.data.jobId}`);
        
        // Wait for manual processing to complete
        console.log('\n⏳ Waiting for manual processing to complete...');
        for (let attempt = 1; attempt <= 15; attempt++) {
          await sleep(5000);
          
          try {
            const statusResponse = await axios.get(`${BASE_URL}/documents/${documentId}/processing-status`, {
              headers: { 'Authorization': AUTH_TOKEN }
            });
            
            console.log(`   Manual processing attempt ${attempt}: Status = ${statusResponse.data.status}, Progress = ${statusResponse.data.progress || 0}%`);
            
            if (statusResponse.data.status === 'completed' || statusResponse.data.status === 'analyzed') {
              processingComplete = true;
              console.log('✅ Manual processing completed successfully!');
              break;
            } else if (statusResponse.data.status === 'failed') {
              console.log('❌ Manual processing also failed');
              if (statusResponse.data.error) {
                console.log(`   Error: ${statusResponse.data.error}`);
              }
              break;
            }
          } catch (error) {
            console.log(`   Manual processing status check ${attempt} failed: ${error.message}`);
          }
        }
      } catch (error) {
        console.log(`❌ Failed to trigger manual processing: ${error.response?.data?.message || error.message}`);
      }
    }

    // Step 4: Check final document state
    console.log('\n🔍 Step 4: Checking final document state...');
    const finalDocResponse = await axios.get(`${BASE_URL}/documents/${documentId}`, {
      headers: { 'Authorization': AUTH_TOKEN }
    });

    const finalDoc = finalDocResponse.data;
    console.log('📄 Final document state:');
    console.log(`   Status: ${finalDoc.status}`);
    console.log(`   Has Content: ${!!finalDoc.content}`);
    console.log(`   Content Length: ${finalDoc.content?.length || 0} characters`);
    
    if (finalDoc.content) {
      console.log(`   Content Preview: "${finalDoc.content.substring(0, 100)}..."`);
    }

    // Step 5: Test chat readiness
    console.log('\n🔍 Step 5: Testing chat readiness...');
    const readinessResponse = await axios.get(`${BASE_URL}/chat/documents/${documentId}/chat-readiness`, {
      headers: { 'Authorization': AUTH_TOKEN }
    });

    console.log('Chat Readiness Result:');
    console.log(`   Ready: ${readinessResponse.data.ready}`);
    console.log(`   Status: ${readinessResponse.data.status}`);
    console.log(`   Message: ${readinessResponse.data.message}`);
    
    if (readinessResponse.data.details) {
      console.log('   Details:', JSON.stringify(readinessResponse.data.details, null, 4));
    }

    // Step 6: Test full chat flow if ready
    if (readinessResponse.data.ready) {
      console.log('\n🎉 Step 6: Document is ready! Testing full chat flow...');

      // Create chat session
      const sessionResponse = await axios.post(`${BASE_URL}/chat/sessions`, {
        documentId: documentId,
        title: 'Service Agreement Analysis'
      }, {
        headers: { 'Authorization': AUTH_TOKEN }
      });

      sessionId = sessionResponse.data.id;
      console.log('✅ Chat session created successfully');
      console.log(`   Session ID: ${sessionId}`);

      // Test multiple chat messages
      const testMessages = [
        'What is this document about?',
        'Who are the parties involved in this agreement?',
        'What are the payment terms and total amount?',
        'What are the key obligations of each party?',
        'How can this agreement be terminated?'
      ];

      for (let i = 0; i < testMessages.length; i++) {
        const message = testMessages[i];
        console.log(`\n📝 Sending message ${i + 1}: "${message}"`);

        try {
          const messageResponse = await axios.post(`${BASE_URL}/chat/messages`, {
            sessionId: sessionId,
            content: message
          }, {
            headers: { 'Authorization': AUTH_TOKEN },
            timeout: 60000
          });

          console.log('   ✅ Response received!');
          console.log(`   Response length: ${messageResponse.data.content?.length || 0} characters`);
          
          if (messageResponse.data.content) {
            const preview = messageResponse.data.content.substring(0, 200);
            console.log(`   Preview: "${preview}${messageResponse.data.content.length > 200 ? '...' : ''}"`);
          }

          await sleep(2000); // Wait between messages
        } catch (error) {
          console.log(`   ❌ Message failed: ${error.response?.data?.message || error.message}`);
        }
      }

      console.log('\n🎉 Complete document processing and chat flow test successful!');
      console.log('\n📋 Summary:');
      console.log('   ✅ Document uploaded and processed successfully');
      console.log('   ✅ Content extracted and validated');
      console.log('   ✅ Chat readiness confirmed');
      console.log('   ✅ Chat session created and tested');
      console.log('   ✅ Multiple chat interactions successful');

    } else {
      console.log('\n⚠️  Document is not ready for chat');
      console.log('This indicates there may still be issues with the processing pipeline.');
    }

  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
    if (error.response) {
      console.error('   Response status:', error.response.status);
      console.error('   Response data:', JSON.stringify(error.response.data, null, 2));
    }
  } finally {
    // Cleanup
    try {
      if (sessionId) {
        console.log('\n🧹 Cleaning up chat session...');
        await axios.delete(`${BASE_URL}/chat/sessions/${sessionId}`, {
          headers: { 'Authorization': AUTH_TOKEN }
        });
        console.log('✅ Chat session deleted');
      }
    } catch (error) {
      console.log('⚠️  Session cleanup warning:', error.message);
    }

    try {
      if (documentId) {
        console.log('🧹 Cleaning up document...');
        await axios.delete(`${BASE_URL}/documents/${documentId}`, {
          headers: { 'Authorization': AUTH_TOKEN }
        });
        console.log('✅ Document deleted');
      }
    } catch (error) {
      console.log('⚠️  Document cleanup warning:', error.message);
    }

    try {
      fs.unlinkSync(testFilePath);
      console.log('✅ Test file cleaned up');
    } catch (error) {
      console.log('⚠️  File cleanup warning:', error.message);
    }
  }
}

testFixedDocumentProcessing();
