// Live document upload and chat test
const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');
const readline = require('readline');

const BASE_URL = 'http://localhost:3000/api';
const AUTH_TOKEN = 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************.kNQXDoCw1m0dfwK6cPmZvDErewqaOAfj2Hrvdwv4yK8';

let documentId = null;
let sessionId = null;

// Create readline interface for interactive chat
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

async function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

async function uploadDocument() {
  console.log('📤 Uploading document...\n');

  // Create a business contract document
  const contractContent = `
BUSINESS PARTNERSHIP AGREEMENT

This Partnership Agreement is made between:
- <PERSON> (Partner A)
- <PERSON> (Partner B)

BUSINESS DETAILS:
Company Name: TechStart Solutions LLC
Business Type: Software Development and Consulting
Start Date: January 1, 2024

PARTNERSHIP TERMS:
1. Profit Sharing: 50/50 split between partners
2. Decision Making: All major decisions require unanimous consent
3. Capital Contributions: Each partner contributes $50,000 initial capital
4. Responsibilities:
   - John Smith: Technical development and project management
   - Sarah Johnson: Business development and client relations

FINANCIAL TERMS:
- Initial Investment: $100,000 total ($50,000 each)
- Operating Expenses: Shared equally
- Revenue Distribution: Monthly profit sharing
- Banking: Joint business account required

OPERATIONAL GUIDELINES:
- Working Hours: Flexible, minimum 30 hours/week each
- Office Space: Shared co-working space downtown
- Equipment: Each partner provides own laptop and tools
- Client Meetings: Both partners should attend major client meetings

INTELLECTUAL PROPERTY:
- All work created during partnership belongs to the company
- Pre-existing IP remains with original owner
- Patents and trademarks filed jointly

DISPUTE RESOLUTION:
- Mediation required before legal action
- Arbitration if mediation fails
- Governing law: State of California

TERMINATION CONDITIONS:
- 90-day notice required for voluntary exit
- Buyout formula: 2x annual profit share
- Non-compete clause: 1 year in same market
- Asset division according to capital contribution ratio

SIGNATURES:
John Smith: _________________ Date: _________
Sarah Johnson: ______________ Date: _________

Witness: ____________________ Date: _________
  `.trim();

  const testFilePath = './business-partnership-agreement.txt';
  fs.writeFileSync(testFilePath, contractContent);

  try {
    const formData = new FormData();
    formData.append('file', fs.createReadStream(testFilePath));
    formData.append('title', 'Business Partnership Agreement');
    formData.append('author', 'Legal Department');

    const uploadResponse = await axios.post(`${BASE_URL}/documents/upload`, formData, {
      headers: {
        ...formData.getHeaders(),
        'Authorization': AUTH_TOKEN
      },
      timeout: 30000
    });

    documentId = uploadResponse.data.document.id;
    console.log('✅ Document uploaded successfully!');
    console.log(`📄 Document ID: ${documentId}`);
    console.log(`📊 Content Length: ${contractContent.length} characters`);
    console.log(`⏳ Status: ${uploadResponse.data.document.processingStatus}`);

    // Clean up test file
    fs.unlinkSync(testFilePath);

    return documentId;
  } catch (error) {
    console.error('❌ Upload failed:', error.message);
    throw error;
  }
}

async function waitForProcessing(docId) {
  console.log('\n⏳ Waiting for document processing...');
  
  for (let attempt = 1; attempt <= 20; attempt++) {
    try {
      const statusResponse = await axios.get(`${BASE_URL}/documents/${docId}/processing-status`, {
        headers: { 'Authorization': AUTH_TOKEN }
      });

      const status = statusResponse.data.status;
      const progress = statusResponse.data.progress || 0;
      
      process.stdout.write(`\r   Processing... Status: ${status}, Progress: ${progress}%`);

      if (status === 'completed' || status === 'analyzed') {
        console.log('\n✅ Document processing completed!');
        return true;
      } else if (status === 'failed') {
        console.log('\n❌ Document processing failed!');
        return false;
      }

      await sleep(2000);
    } catch (error) {
      console.log(`\n⚠️  Status check failed: ${error.message}`);
    }
  }

  console.log('\n⏰ Processing timeout - but let\'s check if it\'s ready for chat anyway...');
  return false;
}

async function checkChatReadiness(docId) {
  console.log('\n🔍 Checking if document is ready for chat...');
  
  try {
    const readinessResponse = await axios.get(`${BASE_URL}/chat/documents/${docId}/chat-readiness`, {
      headers: { 'Authorization': AUTH_TOKEN }
    });

    console.log(`📋 Chat Readiness: ${readinessResponse.data.ready ? '✅ READY' : '❌ NOT READY'}`);
    console.log(`📝 Status: ${readinessResponse.data.status}`);
    console.log(`💬 Message: ${readinessResponse.data.message}`);

    if (readinessResponse.data.details) {
      const details = readinessResponse.data.details;
      console.log(`📊 Content Length: ${details.contentLength || 0} characters`);
      console.log(`📄 Document Status: ${details.documentStatus || 'unknown'}`);
    }

    return readinessResponse.data.ready;
  } catch (error) {
    console.error('❌ Chat readiness check failed:', error.message);
    return false;
  }
}

async function createChatSession(docId) {
  console.log('\n💬 Creating chat session...');
  
  try {
    const sessionResponse = await axios.post(`${BASE_URL}/chat/sessions`, {
      documentId: docId,
      title: 'Partnership Agreement Discussion'
    }, {
      headers: { 'Authorization': AUTH_TOKEN }
    });

    sessionId = sessionResponse.data.id;
    console.log('✅ Chat session created successfully!');
    console.log(`🆔 Session ID: ${sessionId}`);
    console.log(`📝 Title: ${sessionResponse.data.title}`);

    return sessionId;
  } catch (error) {
    console.error('❌ Failed to create chat session:', error.message);
    if (error.response) {
      console.error('Response:', error.response.data);
    }
    throw error;
  }
}

async function sendMessage(message) {
  try {
    console.log(`\n🤖 AI is thinking...`);
    
    const messageResponse = await axios.post(`${BASE_URL}/chat/messages`, {
      sessionId: sessionId,
      content: message
    }, {
      headers: { 'Authorization': AUTH_TOKEN },
      timeout: 60000
    });

    const response = messageResponse.data.content;
    console.log(`\n🤖 AI Response:\n${response}\n`);
    
    return response;
  } catch (error) {
    console.error('❌ Failed to send message:', error.message);
    if (error.response) {
      console.error('Response:', error.response.data);
    }
    return null;
  }
}

function askQuestion(question) {
  return new Promise((resolve) => {
    rl.question(question, (answer) => {
      resolve(answer);
    });
  });
}

async function startInteractiveChat() {
  console.log('\n🎉 Welcome to Interactive Document Chat!');
  console.log('💡 You can now ask questions about the Business Partnership Agreement');
  console.log('💡 Type "exit" to end the chat session');
  console.log('💡 Type "help" for suggested questions\n');

  const suggestedQuestions = [
    "What is this partnership agreement about?",
    "Who are the partners and what are their roles?",
    "How are profits shared between the partners?",
    "What are the capital contribution requirements?",
    "How can the partnership be terminated?",
    "What are the dispute resolution procedures?",
    "What intellectual property terms are included?"
  ];

  while (true) {
    const userInput = await askQuestion('💬 Your question: ');
    
    if (userInput.toLowerCase() === 'exit') {
      console.log('\n👋 Ending chat session...');
      break;
    }
    
    if (userInput.toLowerCase() === 'help') {
      console.log('\n💡 Suggested questions:');
      suggestedQuestions.forEach((q, i) => {
        console.log(`   ${i + 1}. ${q}`);
      });
      console.log('');
      continue;
    }
    
    if (userInput.trim() === '') {
      console.log('⚠️  Please enter a question or type "exit" to quit.\n');
      continue;
    }

    await sendMessage(userInput);
  }
}

async function cleanup() {
  try {
    if (sessionId) {
      console.log('🧹 Cleaning up chat session...');
      await axios.delete(`${BASE_URL}/chat/sessions/${sessionId}`, {
        headers: { 'Authorization': AUTH_TOKEN }
      });
      console.log('✅ Chat session deleted');
    }

    if (documentId) {
      console.log('🧹 Cleaning up document...');
      await axios.delete(`${BASE_URL}/documents/${documentId}`, {
        headers: { 'Authorization': AUTH_TOKEN }
      });
      console.log('✅ Document deleted');
    }
  } catch (error) {
    console.log('⚠️  Cleanup warning:', error.message);
  }
}

async function main() {
  try {
    console.log('🚀 Starting Live Document Upload and Chat Test\n');

    // Step 1: Upload document
    await uploadDocument();

    // Step 2: Wait for processing
    await waitForProcessing(documentId);

    // Step 3: Check chat readiness
    const isReady = await checkChatReadiness(documentId);

    if (!isReady) {
      console.log('❌ Document is not ready for chat. Exiting...');
      return;
    }

    // Step 4: Create chat session
    await createChatSession(documentId);

    // Step 5: Start interactive chat
    await startInteractiveChat();

  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
  } finally {
    await cleanup();
    rl.close();
    console.log('\n✅ Test completed!');
  }
}

// Handle Ctrl+C gracefully
process.on('SIGINT', async () => {
  console.log('\n\n🛑 Interrupted by user');
  await cleanup();
  rl.close();
  process.exit(0);
});

main();
