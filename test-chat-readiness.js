// Simple test for chat readiness endpoint
const axios = require('axios');

const BASE_URL = 'http://localhost:3000/api';
const AUTH_TOKEN = 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************.kNQXDoCw1m0dfwK6cPmZvDErewqaOAfj2Hrvdwv4yK8';
const DOCUMENT_ID = '651eac23-a513-41b0-9728-e5e5838f42e2';

async function testChatReadiness() {
  console.log('🔍 Testing Chat Readiness Endpoint...\n');

  try {
    console.log(`Testing document ID: ${DOCUMENT_ID}`);
    
    const response = await axios.get(`${BASE_URL}/chat/documents/${DOCUMENT_ID}/chat-readiness`, {
      headers: { 'Authorization': AUTH_TOKEN },
      timeout: 10000
    });

    console.log('✅ Chat readiness response received:');
    console.log('Status:', response.status);
    console.log('Data:', JSON.stringify(response.data, null, 2));

    if (response.data.ready) {
      console.log('\n🎉 Document is ready for chat!');
      
      // Test creating a chat session
      console.log('\n💬 Testing chat session creation...');
      const sessionResponse = await axios.post(`${BASE_URL}/chat/sessions`, {
        documentId: DOCUMENT_ID,
        title: 'Test Chat Session'
      }, {
        headers: { 'Authorization': AUTH_TOKEN }
      });

      console.log('✅ Chat session created successfully:');
      console.log('Session ID:', sessionResponse.data.id);
      console.log('Title:', sessionResponse.data.title);

      // Test sending a message
      console.log('\n📝 Testing chat message...');
      const messageResponse = await axios.post(`${BASE_URL}/chat/messages`, {
        sessionId: sessionResponse.data.id,
        content: 'What is this document about?'
      }, {
        headers: { 'Authorization': AUTH_TOKEN },
        timeout: 30000
      });

      console.log('✅ Message sent and response received:');
      console.log('Response length:', messageResponse.data.content?.length || 0);
      if (messageResponse.data.content) {
        console.log('Response preview:', messageResponse.data.content.substring(0, 200) + '...');
      }

      // Cleanup
      console.log('\n🧹 Cleaning up...');
      await axios.delete(`${BASE_URL}/chat/sessions/${sessionResponse.data.id}`, {
        headers: { 'Authorization': AUTH_TOKEN }
      });
      console.log('✅ Chat session deleted');

    } else {
      console.log('\n⚠️  Document is not ready for chat:');
      console.log('Status:', response.data.status);
      console.log('Message:', response.data.message);
      if (response.data.details) {
        console.log('Details:', JSON.stringify(response.data.details, null, 2));
      }
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
  }
}

testChatReadiness();
