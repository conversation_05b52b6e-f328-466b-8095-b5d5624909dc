# Document Chat Readiness Guide

This guide explains how to check if a document is ready for chat and handle various document states in the frontend.

## API Endpoint

### Check Document Chat Readiness

**Endpoint:** `GET /api/chat/documents/:documentId/chat-readiness`

**Description:** Check if a document is ready for chat interaction.

**Response:**
```typescript
interface DocumentChatReadiness {
  ready: boolean;
  status: string;
  message: string;
  details?: {
    documentStatus?: string;
    contentLength?: number;
    hasMetadata?: boolean;
    sectionsCount?: number;
    processingError?: any;
    currentStatus?: string;
    minimumRequired?: number;
  };
}
```

## Document Status Types

### Ready States
- `ready` - Document is fully processed and ready for chat

### Not Ready States
- `not_found` - Document doesn't exist
- `failed` - Document processing failed
- `processing` - Document is still being processed
- `invalid_status` - Document has an invalid status
- `no_content` - Document has no content
- `insufficient_content` - Document content is too short
- `error` - Error occurred while checking

## Frontend Implementation

### React Hook for Document Chat Readiness

```typescript
import { useState, useEffect } from 'react';

interface DocumentChatReadiness {
  ready: boolean;
  status: string;
  message: string;
  details?: any;
}

export function useDocumentChatReadiness(documentId: string | null) {
  const [readiness, setReadiness] = useState<DocumentChatReadiness | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!documentId) {
      setReadiness(null);
      return;
    }

    const checkReadiness = async () => {
      setLoading(true);
      setError(null);
      
      try {
        const response = await fetch(`/api/chat/documents/${documentId}/chat-readiness`);
        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        const data = await response.json();
        setReadiness(data);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to check document readiness');
        setReadiness(null);
      } finally {
        setLoading(false);
      }
    };

    checkReadiness();
  }, [documentId]);

  return { readiness, loading, error };
}
```

### Document Status Component

```typescript
import React from 'react';
import { useDocumentChatReadiness } from './hooks/useDocumentChatReadiness';

interface DocumentStatusProps {
  documentId: string;
  onReadyForChat?: () => void;
}

export function DocumentStatus({ documentId, onReadyForChat }: DocumentStatusProps) {
  const { readiness, loading, error } = useDocumentChatReadiness(documentId);

  if (loading) {
    return (
      <div className="flex items-center space-x-2">
        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
        <span>Checking document status...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-red-600 text-sm">
        Error: {error}
      </div>
    );
  }

  if (!readiness) {
    return null;
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ready': return 'text-green-600';
      case 'processing': return 'text-yellow-600';
      case 'failed': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'ready': return '✅';
      case 'processing': return '⏳';
      case 'failed': return '❌';
      default: return '⚠️';
    }
  };

  return (
    <div className="space-y-2">
      <div className={`flex items-center space-x-2 ${getStatusColor(readiness.status)}`}>
        <span>{getStatusIcon(readiness.status)}</span>
        <span className="font-medium">{readiness.message}</span>
      </div>
      
      {readiness.details && (
        <div className="text-sm text-gray-600 space-y-1">
          {readiness.details.documentStatus && (
            <div>Status: {readiness.details.documentStatus}</div>
          )}
          {readiness.details.contentLength && (
            <div>Content length: {readiness.details.contentLength.toLocaleString()} characters</div>
          )}
          {readiness.details.sectionsCount !== undefined && (
            <div>Sections: {readiness.details.sectionsCount}</div>
          )}
        </div>
      )}
      
      {readiness.ready && onReadyForChat && (
        <button
          onClick={onReadyForChat}
          className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors"
        >
          Start Chat
        </button>
      )}
      
      {!readiness.ready && readiness.status === 'processing' && (
        <div className="text-sm text-gray-600">
          Please wait for document processing to complete before starting a chat.
        </div>
      )}
    </div>
  );
}
```

### Chat Session Creation with Validation

```typescript
import React, { useState } from 'react';
import { DocumentStatus } from './DocumentStatus';

interface ChatSessionCreatorProps {
  documentId: string;
}

export function ChatSessionCreator({ documentId }: ChatSessionCreatorProps) {
  const [isCreating, setIsCreating] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleCreateSession = async () => {
    setIsCreating(true);
    setError(null);

    try {
      const response = await fetch('/api/chat/sessions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          documentId,
          title: 'Document Chat Session',
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to create chat session');
      }

      const session = await response.json();
      // Redirect to chat session or handle success
      window.location.href = `/chat/sessions/${session.id}`;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create chat session');
    } finally {
      setIsCreating(false);
    }
  };

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold">Start Chat Session</h3>
      
      <DocumentStatus 
        documentId={documentId} 
        onReadyForChat={handleCreateSession}
      />
      
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
          {error}
        </div>
      )}
      
      {isCreating && (
        <div className="text-blue-600">
          Creating chat session...
        </div>
      )}
    </div>
  );
}
```

## Error Handling Best Practices

1. **Always check document readiness** before allowing users to start a chat
2. **Provide clear feedback** about document status and what users need to do
3. **Handle processing states** gracefully with appropriate loading indicators
4. **Retry mechanisms** for temporary failures
5. **Clear error messages** that guide users to resolution

## Common Error Scenarios

### Document Still Processing
- Show progress indicator
- Provide estimated completion time if available
- Allow users to check back later

### Document Processing Failed
- Suggest re-uploading the document
- Provide contact information for support
- Show specific error details if helpful

### Document Content Issues
- Explain minimum content requirements
- Suggest uploading a different document
- Provide examples of suitable documents
