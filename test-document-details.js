// Test to check document details and processing status
const axios = require('axios');

const BASE_URL = 'http://localhost:3000/api';
const AUTH_TOKEN = 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************.kNQXDoCw1m0dfwK6cPmZvDErewqaOAfj2Hrvdwv4yK8';

async function testDocumentDetails() {
  console.log('🔍 Testing Document Details...\n');

  try {
    // Get list of documents
    console.log('📋 Getting list of documents...');
    const documentsResponse = await axios.get(`${BASE_URL}/documents`, {
      headers: { 'Authorization': AUTH_TOKEN }
    });

    console.log('Response structure:', Object.keys(documentsResponse.data));

    const documents = documentsResponse.data.items || documentsResponse.data.documents || documentsResponse.data || [];
    console.log(`Found ${documents.length} documents`);

    if (documents.length > 0) {
      const latestDoc = documents[0];
      console.log('\n📄 Latest document details:');
      console.log('   ID:', latestDoc.id);
      console.log('   Name:', latestDoc.originalName);
      console.log('   Status:', latestDoc.status);
      console.log('   Size:', latestDoc.size);
      console.log('   MIME Type:', latestDoc.mimeType);
      console.log('   Upload Date:', latestDoc.uploadDate);
      console.log('   Has Content:', !!latestDoc.content);
      console.log('   Content Length:', latestDoc.content?.length || 0);

      if (latestDoc.metadata) {
        console.log('   Metadata keys:', Object.keys(latestDoc.metadata));
      }

      if (latestDoc.processingError) {
        console.log('   Processing Error:', latestDoc.processingError);
      }

      // Test chat readiness for this document
      console.log('\n🔍 Testing chat readiness...');
      const readinessResponse = await axios.get(`${BASE_URL}/chat/documents/${latestDoc.id}/chat-readiness`, {
        headers: { 'Authorization': AUTH_TOKEN }
      });

      console.log('Chat Readiness:');
      console.log('   Ready:', readinessResponse.data.ready);
      console.log('   Status:', readinessResponse.data.status);
      console.log('   Message:', readinessResponse.data.message);
      
      if (readinessResponse.data.details) {
        console.log('   Details:', JSON.stringify(readinessResponse.data.details, null, 4));
      }

      // If document has failed, let's try to manually trigger processing
      if (latestDoc.status === 'failed' || latestDoc.status === 'uploaded') {
        console.log('\n🔄 Attempting to manually trigger processing...');
        try {
          const processResponse = await axios.post(`${BASE_URL}/documents/${latestDoc.id}/process`, {}, {
            headers: { 'Authorization': AUTH_TOKEN }
          });
          console.log('✅ Processing triggered:', processResponse.data);
        } catch (error) {
          console.log('❌ Failed to trigger processing:', error.response?.data || error.message);
        }
      }

      // If ready, test creating a chat session
      if (readinessResponse.data.ready) {
        console.log('\n💬 Testing chat session creation...');
        try {
          const sessionResponse = await axios.post(`${BASE_URL}/chat/sessions`, {
            documentId: latestDoc.id,
            title: 'Test Chat Session'
          }, {
            headers: { 'Authorization': AUTH_TOKEN }
          });

          console.log('✅ Chat session created successfully!');
          console.log('   Session ID:', sessionResponse.data.id);

          // Test sending a message
          console.log('\n📝 Testing chat message...');
          const messageResponse = await axios.post(`${BASE_URL}/chat/messages`, {
            sessionId: sessionResponse.data.id,
            content: 'What is this document about?'
          }, {
            headers: { 'Authorization': AUTH_TOKEN },
            timeout: 30000
          });

          console.log('✅ Message sent successfully!');
          console.log('   Response length:', messageResponse.data.content?.length || 0);
          if (messageResponse.data.content) {
            console.log('   Response preview:', messageResponse.data.content.substring(0, 200) + '...');
          }

          // Cleanup
          await axios.delete(`${BASE_URL}/chat/sessions/${sessionResponse.data.id}`, {
            headers: { 'Authorization': AUTH_TOKEN }
          });
          console.log('✅ Chat session cleaned up');

        } catch (error) {
          console.log('❌ Chat test failed:', error.response?.data || error.message);
        }
      }
    } else {
      console.log('No documents found. Upload a document first.');
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (error.response) {
      console.error('   Response status:', error.response.status);
      console.error('   Response data:', JSON.stringify(error.response.data, null, 2));
    }
  }
}

testDocumentDetails();
