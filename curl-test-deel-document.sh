#!/bin/bash

# Deel Document Upload and Chat Test using curl
# This script tests the complete document upload to chat flow using curl commands

BASE_URL="http://localhost:3000/api"
AUTH_TOKEN="Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************.kNQXDoCw1m0dfwK6cPmZvDErewqaOAfj2Hrvdwv4yK8"
DEEL_DOCUMENT="./deel-Osazee Agbonze - Turing remote work agreement - Google-contract-m6e4r22.pdf"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_step() {
    echo -e "${BLUE}🔹 $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

# Function to extract JSON value
extract_json_value() {
    echo "$1" | grep -o "\"$2\":[^,}]*" | cut -d':' -f2 | tr -d '"' | tr -d ' '
}

echo -e "${BLUE}🚀 Starting Deel Document Upload and Chat Test with curl${NC}"
echo ""

# Step 1: Check if Deel document exists
print_step "Step 1: Checking if Deel document exists..."
if [ ! -f "$DEEL_DOCUMENT" ]; then
    print_error "Deel document not found at: $DEEL_DOCUMENT"
    exit 1
fi

file_size=$(stat -f%z "$DEEL_DOCUMENT" 2>/dev/null || stat -c%s "$DEEL_DOCUMENT" 2>/dev/null)
print_success "Found Deel document (Size: $((file_size / 1024)) KB)"

# Step 2: Upload the document
print_step "Step 2: Uploading Deel document..."
echo "📤 Uploading: $(basename "$DEEL_DOCUMENT")"

upload_response=$(curl -s -X POST "$BASE_URL/documents/upload" \
  -H "Authorization: $AUTH_TOKEN" \
  -F "file=@$DEEL_DOCUMENT" \
  -F "title=Deel Remote Work Agreement - Osazee Agbonze" \
  -F "author=Deel Legal Team")

echo "Upload Response:"
echo "$upload_response" | jq '.' 2>/dev/null || echo "$upload_response"

# Extract document ID
document_id=$(echo "$upload_response" | jq -r '.document.id' 2>/dev/null)
if [ "$document_id" = "null" ] || [ -z "$document_id" ]; then
    print_error "Failed to extract document ID from upload response"
    exit 1
fi

print_success "Document uploaded successfully! ID: $document_id"

# Step 3: Wait for processing and monitor status
print_step "Step 3: Monitoring document processing..."
max_attempts=20
attempt=1

while [ $attempt -le $max_attempts ]; do
    echo "📊 Checking processing status (Attempt $attempt/$max_attempts)..."
    
    status_response=$(curl -s -X GET "$BASE_URL/documents/$document_id/processing-status" \
      -H "Authorization: $AUTH_TOKEN")
    
    echo "Status Response:"
    echo "$status_response" | jq '.' 2>/dev/null || echo "$status_response"
    
    status=$(echo "$status_response" | jq -r '.status' 2>/dev/null)
    progress=$(echo "$status_response" | jq -r '.progress' 2>/dev/null)
    
    echo "Status: $status, Progress: $progress%"
    
    if [ "$status" = "completed" ] || [ "$status" = "analyzed" ]; then
        print_success "Document processing completed!"
        break
    elif [ "$status" = "failed" ]; then
        print_error "Document processing failed!"
        error_msg=$(echo "$status_response" | jq -r '.error' 2>/dev/null)
        if [ "$error_msg" != "null" ] && [ -n "$error_msg" ]; then
            echo "Error: $error_msg"
        fi
        exit 1
    fi
    
    sleep 3
    attempt=$((attempt + 1))
done

if [ $attempt -gt $max_attempts ]; then
    print_warning "Processing timeout, but continuing to check chat readiness..."
fi

# Step 4: Check chat readiness
print_step "Step 4: Checking chat readiness..."

readiness_response=$(curl -s -X GET "$BASE_URL/chat/documents/$document_id/chat-readiness" \
  -H "Authorization: $AUTH_TOKEN")

echo "Chat Readiness Response:"
echo "$readiness_response" | jq '.' 2>/dev/null || echo "$readiness_response"

ready=$(echo "$readiness_response" | jq -r '.ready' 2>/dev/null)
readiness_status=$(echo "$readiness_response" | jq -r '.status' 2>/dev/null)
readiness_message=$(echo "$readiness_response" | jq -r '.message' 2>/dev/null)

echo "Ready: $ready"
echo "Status: $readiness_status"
echo "Message: $readiness_message"

if [ "$ready" != "true" ]; then
    print_error "Document is not ready for chat: $readiness_message"
    exit 1
fi

print_success "Document is ready for chat!"

# Step 5: Create chat session
print_step "Step 5: Creating chat session..."

session_response=$(curl -s -X POST "$BASE_URL/chat/sessions" \
  -H "Authorization: $AUTH_TOKEN" \
  -H "Content-Type: application/json" \
  -d "{
    \"documentId\": \"$document_id\",
    \"title\": \"Deel Contract Analysis\"
  }")

echo "Session Response:"
echo "$session_response" | jq '.' 2>/dev/null || echo "$session_response"

session_id=$(echo "$session_response" | jq -r '.id' 2>/dev/null)
if [ "$session_id" = "null" ] || [ -z "$session_id" ]; then
    print_error "Failed to create chat session"
    exit 1
fi

print_success "Chat session created! ID: $session_id"

# Step 6: Send test messages
print_step "Step 6: Testing chat messages..."

# Test message 1
echo ""
echo "💬 Question 1: What is this Deel contract about?"
message1_response=$(curl -s -X POST "$BASE_URL/chat/messages" \
  -H "Authorization: $AUTH_TOKEN" \
  -H "Content-Type: application/json" \
  -d "{
    \"sessionId\": \"$session_id\",
    \"content\": \"What is this Deel contract about and who are the parties involved?\"
  }")

echo "🤖 AI Response 1:"
echo "$message1_response" | jq -r '.content' 2>/dev/null || echo "$message1_response"
echo ""

# Test message 2
echo "💬 Question 2: What are the compensation terms?"
message2_response=$(curl -s -X POST "$BASE_URL/chat/messages" \
  -H "Authorization: $AUTH_TOKEN" \
  -H "Content-Type: application/json" \
  -d "{
    \"sessionId\": \"$session_id\",
    \"content\": \"What are the compensation and payment terms in this contract?\"
  }")

echo "🤖 AI Response 2:"
echo "$message2_response" | jq -r '.content' 2>/dev/null || echo "$message2_response"
echo ""

# Test message 3
echo "💬 Question 3: What are the termination conditions?"
message3_response=$(curl -s -X POST "$BASE_URL/chat/messages" \
  -H "Authorization: $AUTH_TOKEN" \
  -H "Content-Type: application/json" \
  -d "{
    \"sessionId\": \"$session_id\",
    \"content\": \"What are the termination conditions and notice requirements?\"
  }")

echo "🤖 AI Response 3:"
echo "$message3_response" | jq -r '.content' 2>/dev/null || echo "$message3_response"
echo ""

# Step 7: Get session history
print_step "Step 7: Retrieving session history..."

history_response=$(curl -s -X GET "$BASE_URL/chat/sessions/$session_id" \
  -H "Authorization: $AUTH_TOKEN")

echo "Session History:"
echo "$history_response" | jq '.' 2>/dev/null || echo "$history_response"

message_count=$(echo "$history_response" | jq '.messages | length' 2>/dev/null)
print_success "Session contains $message_count messages"

# Step 8: Cleanup
print_step "Step 8: Cleaning up..."

# Delete chat session
echo "🧹 Deleting chat session..."
delete_session_response=$(curl -s -X DELETE "$BASE_URL/chat/sessions/$session_id" \
  -H "Authorization: $AUTH_TOKEN")

if [ $? -eq 0 ]; then
    print_success "Chat session deleted"
else
    print_warning "Failed to delete chat session"
fi

# Delete document
echo "🧹 Deleting document..."
delete_doc_response=$(curl -s -X DELETE "$BASE_URL/documents/$document_id" \
  -H "Authorization: $AUTH_TOKEN")

if [ $? -eq 0 ]; then
    print_success "Document deleted"
else
    print_warning "Failed to delete document"
fi

echo ""
print_success "🎉 Deel document upload and chat test completed successfully!"
echo ""
echo "📋 Summary:"
echo "   ✅ Document uploaded and processed"
echo "   ✅ Chat readiness validated"
echo "   ✅ Chat session created"
echo "   ✅ Multiple chat messages tested"
echo "   ✅ Session history retrieved"
echo "   ✅ Cleanup completed"
