// Simple test to upload a document and check processing
const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');

const BASE_URL = 'http://localhost:3000/api';
const AUTH_TOKEN = 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************.kNQXDoCw1m0dfwK6cPmZvDErewqaOAfj2Hrvdwv4yK8';

async function testSimpleUpload() {
  console.log('📤 Testing Simple Document Upload...\n');

  try {
    // Create a simple text file
    const testContent = `
SIMPLE CONTRACT AGREEMENT

This is a test contract document for testing purposes.

PARTIES:
- Company A (Provider)
- Company B (Client)

TERMS:
1. Service delivery within 30 days
2. Payment: $10,000
3. Confidentiality required

SIGNATURES:
Provider: ________________
Client: ________________

Date: January 15, 2024
    `.trim();

    const testFilePath = './simple-test.txt';
    fs.writeFileSync(testFilePath, testContent);

    console.log('📄 Created test document with content length:', testContent.length);

    // Upload the document
    const formData = new FormData();
    formData.append('file', fs.createReadStream(testFilePath));
    formData.append('title', 'Simple Test Contract');
    formData.append('author', 'Test User');

    console.log('⬆️  Uploading document...');
    const uploadResponse = await axios.post(`${BASE_URL}/documents/upload`, formData, {
      headers: {
        ...formData.getHeaders(),
        'Authorization': AUTH_TOKEN
      },
      timeout: 30000
    });

    const documentId = uploadResponse.data.document.id;
    console.log('✅ Document uploaded successfully');
    console.log('   Document ID:', documentId);
    console.log('   Initial Status:', uploadResponse.data.document.processingStatus);

    // Wait and check processing status
    console.log('\n⏳ Monitoring processing status...');
    let attempts = 0;
    const maxAttempts = 20;

    while (attempts < maxAttempts) {
      await new Promise(resolve => setTimeout(resolve, 3000)); // Wait 3 seconds
      attempts++;

      try {
        const statusResponse = await axios.get(`${BASE_URL}/documents/${documentId}/processing-status`, {
          headers: { 'Authorization': AUTH_TOKEN }
        });

        console.log(`   Attempt ${attempts}: Status = ${statusResponse.data.status}, Progress = ${statusResponse.data.progress || 0}%`);

        if (statusResponse.data.status === 'completed' || statusResponse.data.status === 'analyzed') {
          console.log('✅ Processing completed successfully!');
          break;
        } else if (statusResponse.data.status === 'failed') {
          console.log('❌ Processing failed');
          if (statusResponse.data.error) {
            console.log('   Error:', statusResponse.data.error);
          }
          break;
        }
      } catch (error) {
        console.log(`   Status check error: ${error.message}`);
      }
    }

    // Test chat readiness
    console.log('\n🔍 Testing chat readiness...');
    const readinessResponse = await axios.get(`${BASE_URL}/chat/documents/${documentId}/chat-readiness`, {
      headers: { 'Authorization': AUTH_TOKEN }
    });

    console.log('Chat Readiness Result:');
    console.log('   Ready:', readinessResponse.data.ready);
    console.log('   Status:', readinessResponse.data.status);
    console.log('   Message:', readinessResponse.data.message);

    if (readinessResponse.data.details) {
      console.log('   Details:', JSON.stringify(readinessResponse.data.details, null, 4));
    }

    // If ready, test chat session creation
    if (readinessResponse.data.ready) {
      console.log('\n💬 Testing chat session creation...');
      const sessionResponse = await axios.post(`${BASE_URL}/chat/sessions`, {
        documentId: documentId,
        title: 'Test Chat Session'
      }, {
        headers: { 'Authorization': AUTH_TOKEN }
      });

      console.log('✅ Chat session created successfully!');
      console.log('   Session ID:', sessionResponse.data.id);

      // Test sending a message
      console.log('\n📝 Testing chat message...');
      const messageResponse = await axios.post(`${BASE_URL}/chat/messages`, {
        sessionId: sessionResponse.data.id,
        content: 'What are the main terms of this contract?'
      }, {
        headers: { 'Authorization': AUTH_TOKEN },
        timeout: 60000
      });

      console.log('✅ Chat message successful!');
      console.log('   Response length:', messageResponse.data.content?.length || 0);
      if (messageResponse.data.content) {
        console.log('   Response preview:', messageResponse.data.content.substring(0, 150) + '...');
      }

      // Cleanup session
      await axios.delete(`${BASE_URL}/chat/sessions/${sessionResponse.data.id}`, {
        headers: { 'Authorization': AUTH_TOKEN }
      });
      console.log('✅ Chat session cleaned up');
    }

    // Cleanup document
    try {
      await axios.delete(`${BASE_URL}/documents/${documentId}`, {
        headers: { 'Authorization': AUTH_TOKEN }
      });
      console.log('✅ Document cleaned up');
    } catch (error) {
      console.log('⚠️  Document cleanup warning:', error.message);
    }

    // Cleanup test file
    fs.unlinkSync(testFilePath);
    console.log('✅ Test file cleaned up');

    console.log('\n🎉 Test completed successfully!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (error.response) {
      console.error('   Response status:', error.response.status);
      console.error('   Response data:', JSON.stringify(error.response.data, null, 2));
    }
  }
}

testSimpleUpload();
