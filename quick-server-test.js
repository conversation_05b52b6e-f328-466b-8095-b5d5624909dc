// Quick test to check if server is running and endpoints are accessible
const axios = require('axios');

const BASE_URL = 'http://localhost:3000/api';

async function quickServerTest() {
  console.log('🔍 Quick Server Test...\n');

  const tests = [
    {
      name: 'Health Check',
      method: 'GET',
      url: `${BASE_URL}/health`,
      expected: 200
    },
    {
      name: 'Documents Endpoint',
      method: 'GET', 
      url: `${BASE_URL}/documents`,
      expected: [200, 401] // May require auth
    },
    {
      name: 'Chat Readiness Endpoint (with fake ID)',
      method: 'GET',
      url: `${BASE_URL}/chat/documents/test-id/chat-readiness`,
      expected: [200, 404] // Should return 404 for non-existent document
    },
    {
      name: 'Chat Sessions Endpoint',
      method: 'GET',
      url: `${BASE_URL}/chat/sessions`,
      expected: [200, 401] // May require auth
    }
  ];

  let passedTests = 0;
  let totalTests = tests.length;

  for (const test of tests) {
    try {
      console.log(`Testing: ${test.name}...`);
      
      const response = await axios({
        method: test.method,
        url: test.url,
        timeout: 5000,
        validateStatus: () => true // Don't throw on any status
      });

      const expectedStatuses = Array.isArray(test.expected) ? test.expected : [test.expected];
      
      if (expectedStatuses.includes(response.status)) {
        console.log(`✅ ${test.name}: Status ${response.status} (Expected)`);
        passedTests++;
      } else {
        console.log(`❌ ${test.name}: Status ${response.status} (Expected ${expectedStatuses.join(' or ')})`);
      }
      
    } catch (error) {
      if (error.code === 'ECONNREFUSED') {
        console.log(`❌ ${test.name}: Server not running`);
      } else {
        console.log(`❌ ${test.name}: ${error.message}`);
      }
    }
  }

  console.log(`\n📊 Results: ${passedTests}/${totalTests} tests passed`);

  if (passedTests === totalTests) {
    console.log('🎉 All tests passed! Server is ready for document upload testing.');
    console.log('\nNext steps:');
    console.log('1. Run the complete flow test: node test-complete-document-chat-flow.js');
    console.log('2. Or follow the manual testing guide: manual-test-guide.md');
  } else {
    console.log('⚠️  Some tests failed. Please check your server configuration.');
    console.log('\nTroubleshooting:');
    console.log('- Make sure server is running: npm run start:dev');
    console.log('- Check if endpoints require authentication');
    console.log('- Verify server is listening on port 3000');
  }
}

quickServerTest();
