import { Test, TestingModule } from '@nestjs/testing';
import { BadRequestException, NotFoundException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { getModelToken } from '@nestjs/mongoose';
import { ChatService } from '../services/chat.service';
import { DocumentProcessingService } from '../../documents/services/document-processing.service';
import { AIService } from '../../ai/services/ai.service';
import { TenantContextService } from '../../tenant/services/tenant-context.service';
import { PromptTemplateService } from '../../ai/services/prompt-template.service';
import { CitationService } from '../../ai/services/citation.service';

describe('ChatService - Document Validation', () => {
  let service: ChatService;
  let documentService: jest.Mocked<DocumentProcessingService>;

  const mockDocument = {
    id: 'test-doc-id',
    organizationId: 'test-org',
    filename: 'test.pdf',
    originalName: 'test.pdf',
    size: 1000,
    uploadDate: new Date(),
    status: 'completed',
    content: 'This is a test document with sufficient content for chat interaction.',
    metadata: {
      sections: [
        { title: 'Section 1', content: 'Content 1', purpose: 'Test purpose' }
      ]
    }
  };

  beforeEach(async () => {
    const mockDocumentService = {
      getDocumentById: jest.fn(),
    };

    const mockAIService = {
      generateSessionTitle: jest.fn().mockResolvedValue('Test Session'),
      generateResponse: jest.fn(),
      generateChatResponse: jest.fn(),
    };

    const mockTenantContext = {
      getCurrentOrganization: jest.fn().mockReturnValue('test-org'),
      getCurrentUserId: jest.fn().mockReturnValue('test-user'),
    };

    const mockPromptTemplateService = {
      generateChatPrompt: jest.fn(),
      generateEnhancedChatPrompt: jest.fn(),
    };

    const mockCitationService = {
      extractCitations: jest.fn().mockResolvedValue([]),
      enrichCitations: jest.fn().mockResolvedValue([]),
    };

    const mockConfigService = {
      get: jest.fn().mockReturnValue('test-value'),
    };

    const mockChatSessionModel = {
      find: jest.fn(),
      findOne: jest.fn(),
      create: jest.fn(),
      save: jest.fn(),
    };

    const mockChatMessageModel = {
      find: jest.fn(),
      findOne: jest.fn(),
      create: jest.fn(),
      save: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ChatService,
        { provide: DocumentProcessingService, useValue: mockDocumentService },
        { provide: AIService, useValue: mockAIService },
        { provide: TenantContextService, useValue: mockTenantContext },
        { provide: PromptTemplateService, useValue: mockPromptTemplateService },
        { provide: CitationService, useValue: mockCitationService },
        { provide: ConfigService, useValue: mockConfigService },
        { provide: getModelToken('ChatSession'), useValue: mockChatSessionModel },
        { provide: getModelToken('ChatMessage'), useValue: mockChatMessageModel },
      ],
    }).compile();

    service = module.get<ChatService>(ChatService);
    documentService = module.get(DocumentProcessingService);
  });

  describe('checkDocumentChatReadiness', () => {
    it('should return ready status for completed document with content', async () => {
      documentService.getDocumentById.mockResolvedValue(mockDocument);

      const result = await service.checkDocumentChatReadiness('test-doc-id');

      expect(result).toEqual({
        ready: true,
        status: 'ready',
        message: 'Document is ready for chat',
        details: {
          documentStatus: 'completed',
          contentLength: mockDocument.content.length,
          hasMetadata: true,
          sectionsCount: 1,
        },
      });
    });

    it('should return not ready for document not found', async () => {
      documentService.getDocumentById.mockResolvedValue(null);

      const result = await service.checkDocumentChatReadiness('non-existent-id');

      expect(result).toEqual({
        ready: false,
        status: 'not_found',
        message: 'Document not found',
      });
    });

    it('should return not ready for failed document', async () => {
      const failedDocument = { ...mockDocument, status: 'failed' };
      documentService.getDocumentById.mockResolvedValue(failedDocument);

      const result = await service.checkDocumentChatReadiness('test-doc-id');

      expect(result).toEqual({
        ready: false,
        status: 'failed',
        message: 'Document processing failed. Please re-upload the document.',
        details: { processingError: undefined },
      });
    });

    it('should return not ready for processing document', async () => {
      const processingDocument = { ...mockDocument, status: 'processing' };
      documentService.getDocumentById.mockResolvedValue(processingDocument);

      const result = await service.checkDocumentChatReadiness('test-doc-id');

      expect(result).toEqual({
        ready: false,
        status: 'processing',
        message: 'Document is still being processed. Please wait for processing to complete.',
        details: { currentStatus: 'processing' },
      });
    });

    it('should return not ready for document without content', async () => {
      const noContentDocument = { ...mockDocument, content: '' };
      documentService.getDocumentById.mockResolvedValue(noContentDocument);

      const result = await service.checkDocumentChatReadiness('test-doc-id');

      expect(result).toEqual({
        ready: false,
        status: 'no_content',
        message: 'Document content is not available. The document may need to be reprocessed.',
        details: { hasContent: false, contentLength: 0 },
      });
    });

    it('should return not ready for document with insufficient content', async () => {
      const shortContentDocument = { ...mockDocument, content: 'Short' };
      documentService.getDocumentById.mockResolvedValue(shortContentDocument);

      const result = await service.checkDocumentChatReadiness('test-doc-id');

      expect(result).toEqual({
        ready: false,
        status: 'insufficient_content',
        message: 'Document content is too short for meaningful chat interaction.',
        details: { contentLength: 5, minimumRequired: 50 },
      });
    });
  });

  describe('createSession validation', () => {
    it('should throw BadRequestException for failed document', async () => {
      const failedDocument = { ...mockDocument, status: 'failed' };
      documentService.getDocumentById.mockResolvedValue(failedDocument);

      await expect(
        service.createSession({ documentId: 'test-doc-id', title: 'Test Session' })
      ).rejects.toThrow(BadRequestException);
    });

    it('should throw BadRequestException for processing document', async () => {
      const processingDocument = { ...mockDocument, status: 'processing' };
      documentService.getDocumentById.mockResolvedValue(processingDocument);

      await expect(
        service.createSession({ documentId: 'test-doc-id', title: 'Test Session' })
      ).rejects.toThrow(BadRequestException);
    });

    it('should throw BadRequestException for document without content', async () => {
      const noContentDocument = { ...mockDocument, content: '' };
      documentService.getDocumentById.mockResolvedValue(noContentDocument);

      await expect(
        service.createSession({ documentId: 'test-doc-id', title: 'Test Session' })
      ).rejects.toThrow(BadRequestException);
    });
  });
});
