import { Process, Processor } from '@nestjs/bull';
import { Logger, Injectable } from '@nestjs/common';
import { Job } from 'bull';
import { ProcessingJobData } from '../interfaces/job-status.interface';
import { DocumentProcessingJobType, ProcessDocumentJobData } from '../interfaces/document-processing.types';
import { DocumentProcessingService } from './document-processing.service';
import { AnalysisResultService } from './analysis-result.service';
import { LegalResearchOrchestratorService } from '../../legal-research/services/legal-research-orchestrator.service';
import { QUEUES } from '../../queue/constants';
import { LegalPatternRecognitionService } from './legal-pattern-recognition.service';
import { DocumentProcessingGateway } from '../gateways/document-processing.gateway';

@Injectable()
@Processor(QUEUES.DOCUMENT_PROCESSING)
export class DocumentProcessingProcessor {
  private readonly logger = new Logger(DocumentProcessingProcessor.name);

  constructor(
    private readonly documentService: DocumentProcessingService,
    private readonly legalResearchOrchestratorService: LegalResearchOrchestratorService,
    private readonly analysisResultService: AnalysisResultService,
    private readonly patternRecognitionService: LegalPatternRecognitionService,
    private readonly processingGateway: DocumentProcessingGateway,
  ) {}

  @Process(DocumentProcessingJobType.PROCESS_DOCUMENT)
  async processDocument(job: Job<ProcessDocumentJobData>) {
    const { documentId } = job.data;
    this.logger.log(`Processing document ${documentId}`);

    try {
      await job.progress(0);

      // Send initial processing update
      await this.processingGateway.sendProcessingUpdate({
        documentId,
        status: 'processing',
        progress: 0,
        message: 'Starting document processing...',
      });

      // Fetch the document to get necessary details like organizationId
      const doc = await this.documentService.getDocumentById(documentId);
      if (!doc) {
        throw new Error(`Document with ID ${documentId} not found.`);
      }
      if (!doc.organizationId) {
        this.logger.warn(`Organization ID missing for document ${documentId}. Skipping enrichment if needed elsewhere.`);
      }

      await this.documentService.updateDocumentStatus(doc.id, 'processing');

      // --- Processing Steps with Progress Updates ---
      const options = job.data.options || {};

      // Step 0: Extract content from uploaded file if not already done
      if (!doc.content || doc.content.trim().length === 0) {
        await this.processingGateway.sendProcessingUpdate({
          documentId,
          status: 'processing',
          progress: 5,
          message: 'Extracting content from uploaded file...',
        });

        try {
          await this.documentService.extractContentFromFile(doc.id);
          this.logger.log(`Content extracted for document ${doc.id}`);
        } catch (error) {
          this.logger.error(`Failed to extract content for document ${doc.id}:`, error);
          throw new Error(`Content extraction failed: ${error.message}`);
        }
      }

      // Step 1: Analyze patterns
      await this.processingGateway.sendProcessingUpdate({
        documentId,
        status: 'processing',
        progress: 10,
        message: 'Analyzing document patterns...',
      });

      await this.documentService.analyzePatterns(doc.id);
      await job.progress(30);

      // Step 2: Extract metadata
      await this.processingGateway.sendProcessingUpdate({
        documentId,
        status: 'processing',
        progress: 40,
        message: 'Extracting document metadata...',
      });

      await this.documentService.extractMetadata(doc.id);
      await job.progress(60);

      // Step 3: Optional summary generation
      if (options.generateSummary) {
        await this.processingGateway.sendProcessingUpdate({
          documentId,
          status: 'processing',
          progress: 70,
          message: 'Generating document summary...',
        });

        await this.documentService.generateSummary(doc.id);
        await job.progress(80);
      }

      // --- Citation Enrichment Step ---
      this.logger.log(`Attempting citation enrichment for document ${documentId}`);
      if (doc.organizationId) { // Ensure we have orgId before fetching analysis
        // Fetch the latest AnalysisResult Mongoose document
        const latestAnalysisDoc = await this.analysisResultService.findLatestByDocumentIdAndOrg(doc.id, doc.organizationId);

        if (latestAnalysisDoc) {
          this.logger.debug(`Found analysis result document ${latestAnalysisDoc._id} for document ${documentId}, proceeding with enrichment.`);
          try {
            // Call enrichment service with a plain JS object copy and original content
            const enrichedAnalysisData = await this.legalResearchOrchestratorService.enrichDocument(
              latestAnalysisDoc.toObject(),
              doc.content, // Pass the original document content
            );

            // Update the analysis document with the enriched citations using the correct path
            if (
              enrichedAnalysisData?.metadata?.legalCitations &&
              Array.isArray(enrichedAnalysisData.metadata.legalCitations) &&
              enrichedAnalysisData.metadata.legalCitations.length > 0
            ) {
              // Map the enriched data to the correct structure
              const enrichedContent = {
                ...enrichedAnalysisData.content,
                citations: enrichedAnalysisData.metadata.legalCitations,
              };

              // Update both the analysis content and metadata
              latestAnalysisDoc.analysisContent = enrichedContent;
              if (!latestAnalysisDoc.aiMetadata) {
                latestAnalysisDoc.aiMetadata = {};
              }
              latestAnalysisDoc.aiMetadata = {
                ...latestAnalysisDoc.aiMetadata,
                ...enrichedAnalysisData.metadata,
              };

              // Save the updated document
              await latestAnalysisDoc.save();
              this.logger.log(
                `Successfully saved ${enrichedAnalysisData.metadata.legalCitations.length} enriched citations for analysis ${latestAnalysisDoc._id}`,
              );
            } else {
              this.logger.warn(
                `Enrichment process did not return valid legalCitations array for analysis ${latestAnalysisDoc._id}`,
              );
            }

          } catch (enrichmentError) {
            this.logger.error(`Error during citation enrichment processing for document ${documentId}:`, enrichmentError);
            // Decide if this error should fail the whole job or just be logged
            // For now, we log it and continue
          }
        } else {
          this.logger.log(`No prior analysis result found for document ${documentId}. Skipping enrichment.`);
        }
      } else {
         this.logger.warn(`Skipping citation enrichment for document ${documentId} due to missing organizationId.`);
      }
      await job.progress(95); // Update progress after enrichment attempt
      // --- End of Citation Enrichment Step ---

      await this.documentService.updateDocumentStatus(doc.id, 'completed');
      await job.progress(100);

      // Send completion notification
      await this.processingGateway.sendProcessingUpdate({
        documentId,
        status: 'completed',
        progress: 100,
        message: 'Document processing completed successfully',
      });

      this.logger.log(`Successfully processed document ${documentId}`);
      return {
        success: true,
        documentId: doc.id,
        message: 'Document processed successfully',
      };
    } catch (error) {
      this.logger.error(`Error processing document ${documentId}:`, error);

      // Send error notification
      await this.processingGateway.sendProcessingUpdate({
        documentId,
        status: 'failed',
        progress: 0,
        error: error.message,
        message: 'Document processing failed',
      });

      // Ensure document ID is available for status update even if doc fetch failed initially
      await this.documentService.updateDocumentStatus(
        documentId, // Use the ID from job data
        'failed',
        error.message,
      );
      throw error; // Re-throw error to mark job as failed in Bull
    }
  }

  @Process(DocumentProcessingJobType.ANALYZE_PATTERNS)
  async analyzePatterns(job: Job<ProcessingJobData>) {
    try {
      this.logger.log(`Analyzing patterns for document ${job.data.documentId}`);
      await job.progress(0);

      const doc = await this.documentService.getDocumentById(job.data.documentId);
      await this.documentService.updateMetadata(doc.id, { patternAnalysis: 'in_progress' });

      // Instead of calling analyzePatterns which would queue another job,
      // directly use the pattern recognition service
      if (doc.content) {
        // Use the LegalPatternRecognitionService directly to analyze the document
        const patternResult = await this.patternRecognitionService.analyzeDocument(
          doc.content,
          doc.id,
          'Identify all legal patterns in this document'
        );
        
        // Update the document with the recognized patterns
        await this.documentService.updateMetadata(doc.id, { 
          patterns: patternResult.patterns,
          patternAnalysis: 'completed',
          lastPatternAnalysis: new Date()
        });
        
        this.logger.log(`Successfully analyzed ${patternResult.patterns.length} patterns for document ${doc.id}`);
      } else {
        this.logger.warn(`No content available for pattern analysis in document ${doc.id}`);
        await this.documentService.updateMetadata(doc.id, { 
          patternAnalysis: 'failed',
          patternAnalysisError: 'No document content available'
        });
      }
      
      await job.progress(100);

      return {
        success: true,
        documentId: doc.id,
        message: 'Pattern analysis completed'
      };
    } catch (error) {
      this.logger.error(`Error analyzing patterns for document ${job.data.documentId}:`, error);
      await this.documentService.updateMetadata(job.data.documentId, { 
        patternAnalysis: 'failed',
        patternAnalysisError: error.message
      });
      throw error;
    }
  }

  @Process(DocumentProcessingJobType.EXTRACT_METADATA)
  async extractMetadata(job: Job<ProcessingJobData>) {
    try {
      this.logger.log(`Extracting metadata for document ${job.data.documentId}`);
      await job.progress(0);

      await this.documentService.extractMetadata(job.data.documentId);
      await job.progress(100);

      return {
        success: true,
        documentId: job.data.documentId,
        message: 'Metadata extraction completed'
      };
    } catch (error) {
      this.logger.error(`Error extracting metadata for document ${job.data.documentId}:`, error);
      throw error;
    }
  }

  @Process(DocumentProcessingJobType.GENERATE_SUMMARY)
  async generateSummary(job: Job<ProcessingJobData>) {
    try {
      this.logger.log(`Generating summary for document ${job.data.documentId}`);
      await job.progress(0);

      await this.documentService.generateSummary(job.data.documentId);
      await job.progress(100);

      return {
        success: true,
        documentId: job.data.documentId,
        message: 'Summary generation completed'
      };
    } catch (error) {
      this.logger.error(`Error generating summary for document ${job.data.documentId}:`, error);
      throw error;
    }
  }

  private async updateJobProgress(job: Job, progress: number): Promise<void> {
    try {
      await job.progress(progress);
    } catch (error) {
      this.logger.error(`Error updating job progress:`, error);
    }
  }
}
