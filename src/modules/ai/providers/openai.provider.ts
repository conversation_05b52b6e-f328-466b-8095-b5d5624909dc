import { Injectable, Logger, Inject, forwardRef } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import OpenAI from 'openai';
import { AIProvider } from '../interfaces/ai-provider.interface';
import { ClauseSection } from '../interfaces/clause-section.interface';
import { DepositionAnalysisResult } from '../interfaces/deposition-analysis.interface';
import { generateDepositionAnalysisPrompt } from '../prompts/deposition-analysis.prompt';
import { PromptTemplateService } from '../../prompt-templates/prompt-template.service';
import { ResultPostProcessingService } from '../../post-processing/services/result-post-processing.service';
import { RetryUtil, RetryConfig } from '../../../common/utils/retry.util';
import {
  RateLimiter,
  RateLimiterConfig,
} from '../../../common/utils/rate-limiter.util';
import { RateLimiterInfo } from '../../../common/interfaces/rate-limiter.interface';
import { DocumentType as EnumDocumentType } from '../../../common/enums/document-type.enum';
import { DocumentType as PromptDocumentType } from '../../prompt-templates/interfaces/prompt-template.interface';
import {
  ChatMessage,
  ChatRole,
} from '../../../common/interfaces/chat.interface';
import {
  AIAnalysisResult,
  AIAnalysisMetadata,
} from '../interfaces/ai-analysis-result.interface';

interface DepositionQuestion {
  text: string;
  category: string;
  targetWitness: string;
  followUps?: string[];
}

interface DepositionQuestionsInput {
  caseContext: string;
  keyIssues: string[];
  targetWitnesses: string[];
  options?: {
    questionCount?: number;
    questionCategories?: string[];
    includeFollowUps?: boolean;
    focusAreas?: string[];
  };
}

interface DepositionQuestionsResponse {
  questions: DepositionQuestion[];
  metadata: {
    totalQuestions: number;
    timestamp: string;
    model: string;
  };
}

@Injectable()
export class OpenAIProvider implements AIProvider {
  private readonly logger = new Logger(OpenAIProvider.name);
  private readonly openai: OpenAI;
  private readonly retryUtil: RetryUtil;
  private readonly rateLimiter: RateLimiter;
  private isInitialized = false;
  private readonly systemPrompt: string;

  constructor(
    private readonly configService: ConfigService,
    @Inject(forwardRef(() => PromptTemplateService))
    private readonly promptTemplateService: PromptTemplateService,
    private readonly postProcessingService: ResultPostProcessingService,
  ) {
    this.logger.log('Initializing OpenAI provider...');

    const apiKey = this.configService.get<string>('OPENAI_API_KEY');
    if (apiKey) {
      this.openai = new OpenAI({
        baseURL: 'https://openrouter.ai/api/v1',
        apiKey,
        defaultHeaders: {
          'HTTP-Referer': this.configService.get<string>('YOUR_SITE_URL') || '',
          'X-Title': this.configService.get<string>('YOUR_SITE_NAME') || '',
        },
      });
      this.isInitialized = true;
      if (this.logger) {
        this.logger.log('OpenAI client initialized successfully');
      }
    }

    // Load system prompt from configuration
    this.systemPrompt =
      this.configService.get<string>('systemPrompt.prompt') || '';
    if (this.logger) {
      this.logger.log('Initialized OpenAIProvider with system prompt');
    }

    // Initialize retry utility
    const retryConfig: RetryConfig = {
      maxRetries: this.configService.get<number>('openai.maxRetries') || 3,
      initialDelayMs:
        this.configService.get<number>('openai.retryInitialDelayMs') || 1000,
      maxDelayMs:
        this.configService.get<number>('openai.retryMaxDelayMs') || 5000,
      backoffFactor:
        this.configService.get<number>('openai.retryBackoffFactor') || 2,
    };
    this.retryUtil = new RetryUtil(retryConfig);

    // Initialize rate limiter
    const rateLimiterConfig: RateLimiterConfig = {
      maxRequests: this.configService.get<number>('openai.maxRequests') || 60,
      windowMs: this.configService.get<number>('openai.windowMs') || 60000,
      throwOnLimit:
        this.configService.get<boolean>('openai.throwOnLimit') || false,
    };
    this.rateLimiter = new RateLimiter(rateLimiterConfig, 'openai');
  }

  // Helper method to get the appropriate model for different tasks
  private getModelForTask(taskType: string): string {
    const defaultModel =
      this.configService.get<string>('OPENAI_MODEL_NAME') ||
      'google/gemini-2.5-flash';

    // Use specific models for different tasks if configured
    switch (taskType) {
      case 'comparison':
        return (
          this.configService.get<string>('OPENAI_COMPARISON_MODEL') ||
          defaultModel
        );
      case 'analysis':
        return (
          this.configService.get<string>('OPENAI_ANALYSIS_MODEL') ||
          defaultModel
        );
      case 'chat':
        return (
          this.configService.get<string>('OPENAI_CHAT_MODEL') || defaultModel
        );
      default:
        return defaultModel;
    }
  }

  // Helper to map internal ChatMessage[] to OpenAI message format
  private mapMessagesToProviderFormat(
    messages: ChatMessage[],
  ): OpenAI.Chat.Completions.ChatCompletionMessageParam[] {
    return messages.map((msg) => ({
      role: msg.role === ChatRole.USER ? 'user' : 'assistant', // Map system role if needed
      content: msg.content,
    }));
  }

  public async generateResponse(
    prompt: string,
    options?: Record<string, any>,
  ): Promise<string> {
    const isAnalysis = options?.type === 'analysis';
    const defaultSystemPrompt =
      this.configService.get<string>(
        isAnalysis
          ? 'analysisPromptConfig.systemMessage'
          : 'chatPromptConfig.systemMessage',
      ) || this.systemPrompt;
    if (!this.isInitialized) {
      const error = new Error('OpenAI provider not initialized');
      if (this.logger) {
        this.logger.error(error.message);
      }
      throw error;
    }

    return this.rateLimiter.executeWithRateLimit<string>(async () => {
      const completion = await this.retryUtil.withRetry(
        async () =>
          this.openai.chat.completions.create({
            model:
              this.configService.get<string>('OPENAI_MODEL_NAME') ||
              'google/gemini-2.5-flash',
            messages: [
              {
                role: 'system',
                content: options?.systemMessage || defaultSystemPrompt,
              },
              {
                role: 'user',
                content: prompt,
              },
            ],
            temperature: options?.temperature || 0.7,
            max_tokens:
              options?.maxTokens ||
              this.configService.get<number>('openai.maxTokens'),
            top_p: options?.topP || 0.95,
            presence_penalty: options?.presencePenalty || 0,
            frequency_penalty: options?.frequencyPenalty || 0,
            response_format: { type: 'text' },
          }),
        'generateResponse',
      );

      return completion.choices[0]?.message?.content || '';
    }, 'generateResponse');
  }

  public async generateChatResponse(
    messages: ChatMessage[],
    options?: Record<string, any>,
  ): Promise<string> {
    if (!this.isInitialized) {
      const error = new Error('OpenAI provider not initialized');
      this.logger?.error(error.message);
      throw error;
    }

    // Get the dedicated chat system prompt
    const chatSystemPrompt =
      this.configService.get<string>('chatPromptConfig.systemMessage') ||
      'You are a helpful assistant.'; // Basic fallback

    const providerMessages = this.mapMessagesToProviderFormat(messages);

    return this.rateLimiter.executeWithRateLimit<string>(async () => {
      const completion = await this.retryUtil.withRetry(
        async () =>
          this.openai.chat.completions.create({
            model:
              this.configService.get<string>('OPENAI_MODEL_NAME') ||
              'google/gemini-2.5-flash', // Consider using a proper OpenAI model here
            messages: [
              {
                role: 'system',
                content: options?.systemMessage || chatSystemPrompt, // Allow override
              },
              ...providerMessages,
            ],
            temperature: options?.temperature || 0.7,
            max_tokens:
              options?.maxTokens ||
              this.configService.get<number>('openai.maxTokens') ||
              1000,
            top_p: options?.topP || 0.95,
            presence_penalty: options?.presencePenalty || 0,
            frequency_penalty: options?.frequencyPenalty || 0,
            response_format: { type: 'text' },
          }),
        'generateChatResponse',
      );

      return completion.choices[0]?.message?.content || '';
    }, 'generateChatResponse');
  }

  public async *generateStreamResponse(
    prompt: string,
    options?: Record<string, any>,
  ): AsyncGenerator<string, void, unknown> {
    if (!this.isInitialized) {
      const error = new Error('OpenAI provider not initialized');
      if (this.logger) {
        this.logger.error(error.message);
      }
      throw error;
    }

    const isTokenAvailable = await this.rateLimiter.executeWithRateLimit(
      async () => true,
      'streamCheck',
    );

    if (!isTokenAvailable) {
      yield 'Rate limit exceeded. Please try again later.';
      return;
    }

    try {
      const stream = await this.openai.chat.completions.create({
        model:
          this.configService.get<string>('OPENAI_MODEL_NAME') ||
          'google/gemini-2.5-flash',
        messages: [
          {
            role: 'system',
            content:
              options?.systemMessage ||
              this.configService.get<string>(
                options?.type === 'analysis'
                  ? 'analysisPromptConfig.systemMessage'
                  : 'chatPromptConfig.systemMessage',
              ) ||
              this.systemPrompt,
          },
          { role: 'user', content: prompt },
        ],
        max_completion_tokens: 1000,
        stream: true,
      });

      for await (const chunk of stream) {
        if (chunk.choices[0]?.delta?.content) {
          yield chunk.choices[0].delta.content;
        } else if (chunk.choices[0]?.finish_reason === 'stop') {
          break;
        }
      }
    } catch (error) {
      if (this.logger) {
        this.logger.error(
          `Error in stream generation: ${error.message}`,
          error.stack,
        );
      }
      yield `Error: ${error.message}`;
    }
  }

  public async analyzeDocument(
    content: string,
    options?: { documentType?: EnumDocumentType; query?: string },
  ): Promise<AIAnalysisResult> {
    if (!this.isInitialized) {
      const error = new Error('OpenAI provider not initialized');
      this.logger.error(error.message);
      throw error;
    }

    const startTime = Date.now();
    const providerName = 'OpenAI';
    let modelUsed: string;
    let promptUsed: string;
    let rateLimitInfo: RateLimiterInfo;
    let detectedType: PromptDocumentType;

    try {
      // 1. Determine Document Type & Get Prompt
      if (options?.documentType) {
        // Map from EnumDocumentType (options) to PromptDocumentType
        detectedType = this.mapDocumentType(options.documentType);
      } else {
        // Detect type, result is already PromptDocumentType
        detectedType = await this.promptTemplateService.detectDocumentType(
          content,
        );
      }

      // detectedType is now consistently PromptDocumentType
      const templateObject =
        this.promptTemplateService.getTemplateByType(detectedType);

      if (!templateObject) {
        throw new Error(
          `Could not find prompt template for type: ${detectedType}`,
        );
      }

      const promptContext = {
        documentContent: content,
        userQuery: options?.query,
      };
      promptUsed = this.promptTemplateService.renderTemplate(
        templateObject.template,
        promptContext,
      );

      const modelToUse =
        templateObject.model ||
        this.configService.get<string>('OPENAI_MODEL_NAME') ||
        'google/gemini-2.5-flash';

      this.logger.log(
        `Analyzing document (Type: ${detectedType}) using model: ${modelToUse}`,
      );

      // 2. Execute with Rate Limiting and Retry
      const completion =
        await this.rateLimiter.executeWithRateLimit<OpenAI.Chat.Completions.ChatCompletion>(
          async () =>
            this.retryUtil.withRetry(
              async () =>
                this.openai.chat.completions.create({
                  model: modelToUse,
                  messages: [
                    { role: 'system', content: this.systemPrompt }, // Use configured system prompt
                    { role: 'user', content: promptUsed },
                  ],
                  temperature: this.configService.get<number>(
                    'openai.temperature',
                    0.5,
                  ),
                  max_tokens: this.configService.get<number>(
                    'openai.maxTokens',
                    2048,
                  ),
                  response_format: { type: 'json_object' },
                }),
              'analyzeDocument-openai',
            ),
          'analyzeDocument',
        );

      const endTime = Date.now();
      rateLimitInfo = this.rateLimiter.getInfo();

      let analysisContent: Record<string, any>;
      try {
        const rawContent = completion.choices[0]?.message?.content;
        if (!rawContent) {
          throw new Error('OpenAI response content is empty.');
        }
        const jsonString = this.extractJSONFromText(rawContent);
        analysisContent = JSON.parse(jsonString);
      } catch (parseError) {
        this.logger.error(
          `Failed to parse JSON response from OpenAI: ${parseError.message}`,
          completion.choices[0]?.message?.content,
        );
        throw new Error(
          `Failed to parse analysis response: ${parseError.message}`,
        );
      }

      // 4. Post-process Result
      const postProcessedResult =
        await this.postProcessingService.processAnalysisResult(
          analysisContent,
          { documentType: detectedType }, // Pass the PromptDocumentType used for template selection
        );

      // Check if post-processing returned an error within its structure
      if ('error' in postProcessedResult.data) {
        this.logger.error(
          `Post-processing failed: ${postProcessedResult.data.error}`,
          postProcessedResult.meta?.warnings,
        );
        // Decide how to handle post-processing errors, e.g., throw or return with original content
        // For now, let's use the processed data even if it's an error structure
        analysisContent = postProcessedResult.data;
      } else {
        // Only update if processing was successful and didn't return an error structure
        analysisContent = postProcessedResult.data as Record<string, any>;
        if (
          postProcessedResult.meta?.warnings &&
          postProcessedResult.meta.warnings.length > 0
        ) {
          this.logger.warn(
            `Post-processing warnings: ${postProcessedResult.meta.warnings.join(
              ', ',
            )}`,
          );
        }
      }

      // 5. Construct Metadata
      modelUsed = completion.model;
      const metadata: AIAnalysisMetadata = {
        provider: providerName,
        modelUsed: modelUsed,
        promptUsed: promptUsed,
        processingTimeMs: endTime - startTime,
        rateLimitInfo: rateLimitInfo,
        // Nest token usage within the 'tokenUsage' object as per the interface
        tokenUsage: {
          promptTokens: completion.usage?.prompt_tokens,
          completionTokens: completion.usage?.completion_tokens,
          totalTokens: completion.usage?.total_tokens,
        },
        finishReason: completion.choices[0].finish_reason,
        systemFingerprint: completion.system_fingerprint,
        logId: completion.id,
        // detectedDocumentType is set earlier if available
      };

      // Ensure the return matches the AIAnalysisResult interface
      return { content: analysisContent, metadata };
    } catch (error) {
      const endTime = Date.now();
      this.logger.error(
        `Error during OpenAI document analysis: ${error.message}`,
        error.stack,
      );
      const metadata: AIAnalysisMetadata = {
        provider: providerName,
        modelUsed: modelUsed || 'unknown',
        promptUsed: promptUsed,
        processingTimeMs: endTime - startTime,
        rateLimitInfo: this.rateLimiter?.getInfo(),
        // Don't include tokenUsage here as the API call failed
        finishReason: error.type || 'error',
      };
      // Return minimal info on error, maybe adjust based on desired error handling
      return {
        content: {
          error: 'AI analysis failed',
          message: error.message,
        },
        metadata,
      };
    }
  }

  public async compareDocuments(
    documents: string[],
    options?: Record<string, any>,
  ): Promise<Record<string, any>> {
    if (!this.isInitialized) {
      const error = new Error('OpenAI provider not initialized');
      if (this.logger) {
        this.logger.error(error.message);
      }
      throw error;
    }

    return this.rateLimiter.executeWithRateLimit<Record<string, any>>(
      async () =>
        this.retryUtil.withRetry(async () => {
          const { multiDocumentComparisonPromptConfig } = await import(
            '../prompts/comparison-prompt.config'
          );
          const documentLabels =
            options?.documentLabels ||
            documents.map((_, i) => `Document ${i + 1}`);

          // Build a structured prompt with document metadata if available
          let prompt = `Compare the following legal documents and provide a structured analysis:\n\n`;

          // Add each document with its label and any available metadata
          documents.forEach((doc, i) => {
            const label = documentLabels[i];
            const documentType =
              options?.documentTypes?.[i] ||
              (i === 0 ? options?.documentType : null);

            // Include document type if available
            if (documentType) {
              prompt += `### ${label} (Type: ${documentType}) ###\n${doc}\n\n`;
            } else {
              prompt += `### ${label} ###\n${doc}\n\n`;
            }
          });

          // Add instructions to identify and compare document sections
          prompt += `\nIMPORTANT: For each key provision or topic you identify:
          1. Reference the specific section number or location in each document where this provision appears
          2. Extract the relevant text from each document
          3. Note any conflicts or inconsistencies between documents
          4. Assess the significance of these differences
          5. Provide a recommendation for addressing any conflicts
          
          Ensure that document types are used consistently throughout the entire analysis. Once you identify a document's type, use that same type identifier in all references to that document.`;

          // Add specific instructions based on the comparison type
          if (options?.comparisonType === 'structural') {
            prompt += `\nFocus primarily on the structural differences between these documents.`;
          } else if (options?.comparisonType === 'content') {
            prompt += `\nFocus primarily on the content differences and their legal implications.`;
          } else {
            prompt += `\nProvide a comprehensive analysis covering both structural and content differences.`;
          }
          if (options?.query) {
            prompt += `\n\nSpecific focus: ${options.query}`;
          }

          try {
            const response = await this.openai.chat.completions.create({
              model: this.getModelForTask('comparison'),
              temperature:
                multiDocumentComparisonPromptConfig.temperature || 0.3,
              max_tokens: multiDocumentComparisonPromptConfig.maxTokens || 4000,
              top_p: multiDocumentComparisonPromptConfig.topP,
              presence_penalty:
                multiDocumentComparisonPromptConfig.presencePenalty,
              frequency_penalty:
                multiDocumentComparisonPromptConfig.frequencyPenalty,
              messages: [
                {
                  role: 'system',
                  content: multiDocumentComparisonPromptConfig.systemMessage,
                },
                { role: 'user', content: prompt },
              ],
              response_format: { type: 'json_object' },
            });

            // Add robust JSON parsing with error recovery
            try {
              // First attempt: direct parse
              return JSON.parse(response.choices[0]?.message?.content || '{}');
            } catch (jsonError) {
              // Second attempt: Try to fix common JSON issues
              try {
                const content = response.choices[0]?.message?.content || '{}';

                // Log the problematic content for debugging (truncated for log size)
                if (this.logger) {
                  this.logger.warn(
                    `Attempting to repair malformed JSON. Error: ${
                      jsonError.message
                    }. Content preview: ${content.substring(0, 100)}...`,
                  );
                }

                // Simple JSON repair for common issues
                let repairedJson = content
                  // Fix unescaped quotes in strings
                  .replace(/(?<!\\)\\(?!["\\/bfnrt]|u[0-9a-fA-F]{4})/g, '\\\\')
                  // Fix unescaped quotes
                  .replace(/(?<!\\)"/g, '\\"')
                  // Remove any control characters
                  .replace(/[\u0000-\u001F\u007F-\u009F]/g, '');

                // Ensure the JSON is properly wrapped
                if (!repairedJson.trim().startsWith('{')) {
                  repairedJson = `{ "content": "${repairedJson}" }`;
                }

                return JSON.parse(repairedJson);
              } catch (repairError) {
                // Third attempt: Return a structured error response instead of throwing
                if (this.logger) {
                  this.logger.error(
                    `Failed to repair JSON: ${repairError.message}`,
                    repairError.stack,
                  );
                }

                // Return a valid JSON with error information instead of throwing
                return {
                  error: true,
                  message: 'Failed to parse AI response as JSON',
                  rawContent: response.choices[0]?.message?.content,
                  comparison: {
                    summary:
                      'The document comparison could not be completed due to a technical issue.',
                    differences: [],
                    similarities: [],
                  },
                };
              }
            }
          } catch (error) {
            if (this.logger) {
              this.logger.error(
                `Error in document comparison: ${error.message}`,
                error.stack,
              );
            }
            // Throw the error instead of returning it in the response body
            // This allows the retry mechanism and higher-level handlers to catch it
            throw new Error(`Document comparison failed: ${error.message}`);
          }
        }, 'compareDocuments'),
      'compareDocuments',
    );
  }

  // Maps EnumDocumentType (used in DTOs/external contracts) to PromptDocumentType (used internally for prompts)
  private mapDocumentType(
    type: EnumDocumentType,
  ): PromptDocumentType | PromptDocumentType.GENERAL {
    switch (type) {
      case EnumDocumentType.CONTRACT:
        return PromptDocumentType.CONTRACT;
      case EnumDocumentType.AGREEMENT:
        return PromptDocumentType.AGREEMENT;
      case EnumDocumentType.LEGAL_OPINION:
        return PromptDocumentType.LEGAL_OPINION;
      case EnumDocumentType.POLICY:
        return PromptDocumentType.POLICY;
      case EnumDocumentType.LEGISLATION:
        return PromptDocumentType.LEGISLATION;
      case EnumDocumentType.COURT_FILING:
        return PromptDocumentType.COURT_FILING;
      default:
        this.logger.warn(
          `Unsupported EnumDocumentType '${type}', falling back to GENERAL.`,
        );
        return PromptDocumentType.GENERAL;
    }
  }

  /**
   * Extracts JSON from a given text, handling various formats.
   * @param text The text to extract JSON from.
   * @returns The extracted JSON string.
   */
  private extractJSONFromText(text: string): string {
    // First try to extract from markdown code blocks
    const markdownRegex = /```(?:json)?\s*([\s\S]*?)\s*```/;
    const markdownMatch = text.match(markdownRegex);

    if (markdownMatch && markdownMatch[1]) {
      try {
        const jsonContent = markdownMatch[1].trim();
        // Validate if it's valid JSON
        JSON.parse(jsonContent);
        return jsonContent;
      } catch (e) {
        this.logger?.warn('Found markdown block but content is not valid JSON');
      }
    }

    // Then try to find raw JSON objects/arrays
    const jsonRegex = /(\{[\s\S]*\})|(\[[\s\S]*\])/;
    const jsonMatch = text.match(jsonRegex);

    if (jsonMatch) {
      try {
        const jsonContent = (jsonMatch[1] || jsonMatch[2]).trim();
        // Validate if it's valid JSON
        JSON.parse(jsonContent);
        return jsonContent;
      } catch (e) {
        this.logger?.warn('Found JSON-like structure but it is not valid JSON');
      }
    }

    // If both attempts fail, try to find and parse any JSON-like structure
    try {
      const trimmed = text.trim();
      const firstChar = trimmed.charAt(0);
      const lastChar = trimmed.charAt(trimmed.length - 1);

      if (
        (firstChar === '{' && lastChar === '}') ||
        (firstChar === '[' && lastChar === ']')
      ) {
        JSON.parse(trimmed);
        return trimmed;
      }
    } catch (e) {
      this.logger?.warn('Final attempt to parse JSON failed');
    }

    this.logger?.error('Could not extract valid JSON from text', {
      textPreview: text.substring(0, 100),
    });
    throw new Error('Failed to extract valid JSON from response');
  }

  public getProviderName(): string {
    return 'OpenAI';
  }

  public async isReady(): Promise<boolean> {
    return this.isInitialized;
  }

  public getRateLimiterUtilization(): RateLimiterInfo {
    return this.rateLimiter.getInfo();
  }

  async identifyClauseSections(
    documentContent: string,
    options?: Record<string, any>,
  ): Promise<ClauseSection[]> {
    this.logger.log('OpenAI Provider: Identifying clause sections in document');

    if (!this.isInitialized) {
      throw new Error('OpenAI provider not initialized');
    }

    // Prepare the prompt for clause identification
    const prompt = `Analyze the following legal document and identify distinct clause sections. For each clause, provide the exact text content, the starting and ending character positions in the document, and the type of clause (e.g., confidentiality, liability, termination, etc.).\n\nDocument:\n${documentContent}\n\nOutput the results in the following JSON format:\n[\n  {\n    "content": "clause text",\n    "startIndex": startPosition,\n    "endIndex": endPosition,\n    "type": "clause type",\n    "confidence": confidenceScore\n  }\n]`;

    try {
      // Use the GPT-4 model for better accuracy with legal text
      const model = this.getModelForTask('legal_analysis');

      const response = await this.rateLimiter.executeWithRateLimit(() =>
        this.retryUtil.withRetry(async () => {
          const result = await this.openai.chat.completions.create({
            model,
            messages: [
              {
                role: 'system',
                content:
                  'You are a legal document expert specializing in contract clause identification and analysis.',
              },
              { role: 'user', content: prompt },
            ],
            temperature: 0.2, // Lower temperature for more deterministic results
            response_format: { type: 'json_object' },
          });
          return result.choices[0]?.message?.content || '{}';
        }, 'identifyClauseSections'),
      );

      // Parse the response
      try {
        const parsedResponse = JSON.parse(this.extractJSONFromText(response));

        // Ensure the response is in the expected format
        if (Array.isArray(parsedResponse)) {
          return parsedResponse.map((clause) => ({
            name: clause.name || `Clause ${clause.type || 'Unnamed'}`,
            category: clause.category || clause.type || 'General',
            content: clause.content,
            startIndex: parseInt(clause.startIndex, 10),
            endIndex: parseInt(clause.endIndex, 10),
            type: clause.type,
            confidence: parseFloat(clause.confidence) || 0.7,
            metadata: clause.metadata || {},
          }));
        }

        this.logger.warn(
          'Unexpected response format from OpenAI for clause identification',
        );
        return [];
      } catch (parseError) {
        this.logger.error(
          `Error parsing clause identification response: ${parseError.message}`,
          parseError.stack,
        );
        return [];
      }
    } catch (error) {
      this.logger.error(
        `Error identifying clause sections with OpenAI: ${error.message}`,
        error.stack,
      );
      return [];
    }
  }

  async optimizeClauseTemplate(
    content: string,
    options?: Record<string, any>,
  ): Promise<string> {
    this.logger.log('OpenAI Provider: Optimizing clause template');

    if (!this.isInitialized) {
      throw new Error('OpenAI provider not initialized');
    }

    // Prepare the prompt for clause template optimization
    const prompt = `You are a legal document expert. Optimize the following legal clause to make it more clear, concise, and legally sound. Maintain the original intent and legal meaning, but improve the language, structure, and readability.\n\nOriginal Clause:\n${content}\n\nOptimized Clause:`;

    try {
      const response = await this.rateLimiter.executeWithRateLimit(() =>
        this.retryUtil.withRetry(async () => {
          const result = await this.openai.chat.completions.create({
            model: this.getModelForTask('legal_analysis'),
            messages: [
              {
                role: 'system',
                content:
                  'You are a legal document expert specializing in contract drafting and optimization.',
              },
              { role: 'user', content: prompt },
            ],
            temperature: 0.3, // Slightly higher temperature for creative improvements
          });
          return result.choices[0]?.message?.content || content;
        }, 'optimizeClauseTemplate'),
      );

      return response.trim();
    } catch (error) {
      this.logger.error(
        `Error optimizing clause template with OpenAI: ${error.message}`,
        error.stack,
      );
      return content; // Return original content if optimization fails
    }
  }

  /**
   * Generate a concise and descriptive title for a chat session based on document content
   * @param document The document object containing content to analyze
   * @returns Promise containing the generated title
   */
  async generateSessionTitle(document: any): Promise<string> {
    this.logger.log('OpenAI Provider: Generating session title');

    if (!this.isInitialized) {
      throw new Error('OpenAI provider not initialized');
    }

    // Extract document content
    const content = document?.content || document?.text || '';
    if (!content || content.length < 10) {
      return 'New Legal Document Analysis';
    }

    // Use a shorter excerpt for title generation
    const excerpt = content.substring(0, 1500);
    const prompt = `Generate a concise, descriptive title (5-7 words) for a legal document analysis session based on this excerpt:\n\n${excerpt}`;

    try {
      const response = await this.rateLimiter.executeWithRateLimit(() =>
        this.retryUtil.withRetry(async () => {
          const result = await this.openai.chat.completions.create({
            model: this.getModelForTask('chat'),
            messages: [
              {
                role: 'system',
                content:
                  'You are a legal document expert. Generate concise, descriptive titles for legal document analysis sessions.',
              },
              { role: 'user', content: prompt },
            ],
            temperature: 0.7, // Higher temperature for creative titles
            max_tokens: 20, // Keep titles short
          });
          return (
            result.choices[0]?.message?.content || 'Legal Document Analysis'
          );
        }, 'generateSessionTitle'),
      );

      // Clean up the response - remove quotes if present
      const title = response.trim().replace(/^"|"$/g, '');
      return title || 'Legal Document Analysis';
    } catch (error) {
      this.logger.error(
        `Error generating session title with OpenAI: ${error.message}`,
        error.stack,
      );
      return 'Legal Document Analysis'; // Return default title if generation fails
    }
  }

  /**
   * Analyze a deposition transcript for credibility, inconsistencies, and cross-examination opportunities
   * @param transcript The deposition transcript text to analyze
   * @param context Additional context for the analysis
   * @returns Promise containing the deposition analysis result
   */
  async analyzeDeposition(
    transcript: string,
    context: {
      caseContext?: string;
      focusAreas?: string[];
    } = {},
  ): Promise<DepositionAnalysisResult> {
    this.logger.log('OpenAI Provider: Analyzing deposition');

    if (!this.isInitialized) {
      throw new Error('OpenAI provider not initialized');
    }

    try {
      // Generate the prompt using the helper function
      const prompt = generateDepositionAnalysisPrompt({
        transcript,
        caseContext: context.caseContext,
        focusAreas: context.focusAreas,
      });

      // Use the rate limiter and retry utility to handle API rate limits
      const response = await this.rateLimiter.executeWithRateLimit(() =>
        this.retryUtil.withRetry(async () => {
          const result = await this.openai.chat.completions.create({
            model: this.getModelForTask('legal_analysis'),
            messages: [
              {
                role: 'system',
                content: `You are a legal AI assistant that analyzes deposition transcripts. Provide detailed, accurate, and legally sound analysis in JSON format.`,
              },
              { role: 'user', content: prompt },
            ],
            temperature: 0.2,
            max_tokens: 4000,
          });
          return result.choices[0]?.message?.content || '{}';
        }, 'analyzeDeposition'),
      );
      return JSON.parse(this.extractJSONFromText(response));
    } catch (error) {
      this.logger.error(
        `Error analyzing deposition with OpenAI: ${error.message}`,
        error.stack,
      );
      throw new Error(`Failed to analyze deposition: ${error.message}`);
    }
  }

  async generateDepositionQuestions(
    context: DepositionQuestionsInput,
  ): Promise<DepositionQuestionsResponse> {
    this.logger.log('OpenAI Provider: Generating deposition questions');

    if (!this.isInitialized) {
      throw new Error('OpenAI provider not initialized');
    }

    try {
      const prompt = JSON.stringify({
        task: 'Generate deposition questions',
        context: {
          case: context.caseContext,
          keyIssues: context.keyIssues,
          targetWitnesses: context.targetWitnesses || [],
          requirements: {
            questionCount: context.options?.questionCount || 5,
            categories: context.options?.questionCategories || [],
            includeFollowUps: context.options?.includeFollowUps || true,
            focusAreas: context.options?.focusAreas || [],
          },
        },
      });

      this.logger.log('Generating deposition questions');

      if (!context.targetWitnesses?.length) {
        throw new Error('At least one target witness must be provided');
      }

      const systemPrompt = `You are a legal expert specializing in deposition preparation. Generate specific questions for each witness based on their role.

Available witnesses:
${context.targetWitnesses.map((w) => `- ${w}`).join('\n')}

Each question must include:
- text: The question text
- category: One of ${JSON.stringify(
        context.options?.questionCategories || ['general'],
      )}
- targetWitness: Must match exactly one of the provided witnesses
- followUps: Optional array of follow-up questions depends on the includeFollowUps flag

Ensure questions are appropriate for each witness's role and expertise.`;
      const response = await this.rateLimiter.executeWithRateLimit(() =>
        this.retryUtil.withRetry(async () => {
          const result = await this.openai.chat.completions.create({
            model: this.getModelForTask('legal_analysis'),
            messages: [
              {
                role: 'system',
                content: systemPrompt,
              },
              { role: 'user', content: prompt },
            ],
            temperature: 0.3,
            response_format: { type: 'json_object' },
          });
          return result.choices[0]?.message?.content || '{}';
        }, 'generateDepositionQuestions'),
      );
      return JSON.parse(this.extractJSONFromText(response));
    } catch (error) {
      this.logger.error(
        `Error generating deposition questions with OpenAI: ${error.message}`,
        error.stack,
      );
      throw new Error(`Failed to generate questions: ${error.message}`);
    }
  }
}
