import { registerAs } from '@nestjs/config';

export const openaiConfig = registerAs('openai', () => ({
  apiKey: process.env.OPENAI_API_KEY,
  modelName: process.env.OPENAI_MODEL_NAME || 'google/gemini-2.5-flash',
  temperature: parseFloat(process.env.OPENAI_TEMPERATURE || '0.7'),
  maxTokens: parseInt(process.env.OPENAI_MAX_TOKENS || '4096', 10),
  retry: {
    maxRetries: parseInt(process.env.OPENAI_RETRY_MAX_ATTEMPTS || '3', 10),
    initialDelayMs: parseInt(
      process.env.OPENAI_RETRY_INITIAL_DELAY_MS || '1000',
      10,
    ),
    maxDelayMs: parseInt(process.env.OPENAI_RETRY_MAX_DELAY_MS || '10000', 10),
    backoffFactor: parseFloat(process.env.OPENAI_RETRY_BACKOFF_FACTOR || '2'),
  },
  rateLimit: {
    maxRequests: parseInt(
      process.env.OPENAI_RATE_LIMIT_MAX_REQUESTS || '3500',
      10,
    ),
    windowMs: parseInt(process.env.OPENAI_RATE_LIMIT_WINDOW_MS || '60000', 10), // Default: 3500 requests per minute
    throwOnLimit: process.env.OPENAI_RATE_LIMIT_THROW_ERROR === 'true',
  },
}));

export type OpenAIConfig = ReturnType<typeof openaiConfig>;
