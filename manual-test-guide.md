# Manual Testing Guide: Document Upload to Chat Flow

This guide provides step-by-step instructions to manually test the complete document upload to chat functionality.

## Prerequisites

1. **Server Running**: Make sure your server is running
   ```bash
   npm run start:dev
   ```

2. **Test Document**: Create a test document with sufficient content (minimum 50 characters)

## Step-by-Step Testing

### Step 1: Upload a Document

**Using cURL:**
```bash
curl -X POST "http://localhost:3000/api/documents/upload" \
  -H "Content-Type: multipart/form-data" \
  -F "file=@/path/to/your/document.pdf" \
  -F "title=Test Document" \
  -F "author=Test Author"
```

**Expected Response:**
```json
{
  "message": "Document uploaded successfully and processing started",
  "document": {
    "id": "document-id-here",
    "filename": "document.pdf",
    "processingStatus": "queued",
    "estimatedProcessingTime": "30-60 seconds"
  }
}
```

**Save the document ID** for the next steps.

### Step 2: Check Processing Status

**Using cURL:**
```bash
curl -X GET "http://localhost:3000/api/documents/{DOCUMENT_ID}/processing-status"
```

**Expected Responses:**

**While Processing:**
```json
{
  "status": "processing",
  "progress": 50,
  "estimatedTimeRemaining": 15000
}
```

**When Complete:**
```json
{
  "status": "completed",
  "progress": 100
}
```

**Keep checking until status is "completed" or "analyzed".**

### Step 3: Check Chat Readiness

**Using cURL:**
```bash
curl -X GET "http://localhost:3000/api/chat/documents/{DOCUMENT_ID}/chat-readiness"
```

**Expected Response (Ready):**
```json
{
  "ready": true,
  "status": "ready",
  "message": "Document is ready for chat",
  "details": {
    "documentStatus": "completed",
    "contentLength": 1250,
    "hasMetadata": true,
    "sectionsCount": 3
  }
}
```

**Expected Response (Not Ready):**
```json
{
  "ready": false,
  "status": "processing",
  "message": "Document is still being processed. Please wait for processing to complete.",
  "details": {
    "currentStatus": "processing"
  }
}
```

### Step 4: Create Chat Session

**Using cURL:**
```bash
curl -X POST "http://localhost:3000/api/chat/sessions" \
  -H "Content-Type: application/json" \
  -d '{
    "documentId": "{DOCUMENT_ID}",
    "title": "Test Chat Session"
  }'
```

**Expected Response:**
```json
{
  "id": "session-id-here",
  "documentId": "document-id-here",
  "title": "Test Chat Session",
  "createdAt": "2024-01-15T10:30:00.000Z"
}
```

**Save the session ID** for the next steps.

### Step 5: Send Chat Messages

**Using cURL:**
```bash
curl -X POST "http://localhost:3000/api/chat/messages" \
  -H "Content-Type: application/json" \
  -d '{
    "sessionId": "{SESSION_ID}",
    "content": "What is this document about?"
  }'
```

**Expected Response:**
```json
{
  "id": "message-id",
  "sessionId": "session-id",
  "role": "assistant",
  "content": "This document appears to be a contract that outlines...",
  "timestamp": "2024-01-15T10:31:00.000Z",
  "citations": []
}
```

### Step 6: Test Streaming Chat

**Using cURL:**
```bash
curl -X POST "http://localhost:3000/api/chat/stream/messages" \
  -H "Content-Type: application/json" \
  -d '{
    "sessionId": "{SESSION_ID}",
    "content": "Can you summarize the key points?"
  }'
```

**Expected Response:**
Server-Sent Events stream with content like:
```
data: {"content": "Based", "role": "assistant"}

data: {"content": " on the", "role": "assistant"}

data: {"content": " document,", "role": "assistant"}
```

### Step 7: Get Session History

**Using cURL:**
```bash
curl -X GET "http://localhost:3000/api/chat/sessions/{SESSION_ID}"
```

**Expected Response:**
```json
{
  "id": "session-id",
  "documentId": "document-id",
  "title": "Test Chat Session",
  "createdAt": "2024-01-15T10:30:00.000Z",
  "updatedAt": "2024-01-15T10:31:00.000Z",
  "messages": [
    {
      "id": "msg-1",
      "role": "user",
      "content": "What is this document about?",
      "timestamp": "2024-01-15T10:30:30.000Z"
    },
    {
      "id": "msg-2",
      "role": "assistant",
      "content": "This document appears to be...",
      "timestamp": "2024-01-15T10:31:00.000Z"
    }
  ]
}
```

## Error Testing

### Test 1: Chat with Unprocessed Document

1. Upload a document
2. Immediately try to create a chat session (before processing completes)

**Expected Error:**
```json
{
  "statusCode": 400,
  "message": "Document is still being processed. Please wait for processing to complete before starting a chat session."
}
```

### Test 2: Chat with Failed Document

1. Upload an invalid file or corrupt document
2. Wait for processing to fail
3. Try to create a chat session

**Expected Error:**
```json
{
  "statusCode": 400,
  "message": "Document processing failed. Please re-upload the document."
}
```

### Test 3: Chat with Non-existent Document

```bash
curl -X POST "http://localhost:3000/api/chat/sessions" \
  -H "Content-Type: application/json" \
  -d '{
    "documentId": "non-existent-id",
    "title": "Test Session"
  }'
```

**Expected Error:**
```json
{
  "statusCode": 404,
  "message": "Document not found: non-existent-id"
}
```

## Cleanup

### Delete Chat Session
```bash
curl -X DELETE "http://localhost:3000/api/chat/sessions/{SESSION_ID}"
```

### Delete Document
```bash
curl -X DELETE "http://localhost:3000/api/documents/{DOCUMENT_ID}"
```

## Troubleshooting

### Common Issues

1. **Document Processing Stuck**: Check Redis connection and document processing queue
2. **Chat Not Working**: Verify document has content and status is 'completed'
3. **Authentication Errors**: Check if endpoints require authentication tokens
4. **Timeout Errors**: Increase timeout for large documents

### Debug Commands

**Check Document Details:**
```bash
curl -X GET "http://localhost:3000/api/documents/{DOCUMENT_ID}"
```

**Check Processing Queue:**
```bash
curl -X GET "http://localhost:3000/api/documents/processing-queue-status"
```

## Success Criteria

✅ Document uploads successfully  
✅ Document processes to 'completed' status  
✅ Chat readiness returns 'ready: true'  
✅ Chat session creates successfully  
✅ Messages send and receive responses  
✅ Streaming chat works  
✅ Session history is retrievable  
✅ Error handling works for invalid scenarios  

If all criteria pass, the document upload to chat flow is working correctly!
