// Complete end-to-end test for document upload to chat flow
const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');
const path = require('path');

const BASE_URL = 'http://localhost:3000/api';
const AUTH_TOKEN = 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************.kNQXDoCw1m0dfwK6cPmZvDErewqaOAfj2Hrvdwv4yK8';

// Create a test document
function createTestDocument() {
  const testContent = `
# Test Contract Document

This is a comprehensive test contract document with sufficient content for chat interaction.

## Article 1: Parties
This agreement is between Company A and Company B for the provision of services.

## Article 2: Terms and Conditions
The following terms and conditions apply:
- Service delivery within 30 days
- Payment terms: Net 30
- Confidentiality requirements
- Termination clauses

## Article 3: Obligations
Both parties agree to fulfill their respective obligations as outlined in this document.

## Article 4: Liability
Limitation of liability clauses and indemnification terms are specified herein.

This document contains sufficient content to enable meaningful chat interactions and analysis.
  `.trim();

  const testFilePath = path.join(__dirname, 'test-document.txt');
  fs.writeFileSync(testFilePath, testContent);
  return testFilePath;
}

async function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

async function testCompleteDocumentChatFlow() {
  console.log('🧪 Testing Complete Document Upload to Chat Flow...\n');

  let documentId = null;
  let sessionId = null;
  const testFilePath = createTestDocument();

  try {
    // Step 1: Upload Document
    console.log('📤 Step 1: Uploading document...');
    const formData = new FormData();
    formData.append('file', fs.createReadStream(testFilePath));
    formData.append('title', 'Test Contract Document');
    formData.append('author', 'Test Author');

    const uploadResponse = await axios.post(`${BASE_URL}/documents/upload`, formData, {
      headers: {
        ...formData.getHeaders(),
        'Authorization': AUTH_TOKEN
      },
      timeout: 30000
    });

    documentId = uploadResponse.data.document.id;
    console.log('✅ Document uploaded successfully');
    console.log(`   Document ID: ${documentId}`);
    console.log(`   Status: ${uploadResponse.data.document.processingStatus}`);

    // Step 2: Wait for document processing
    console.log('\n⏳ Step 2: Waiting for document processing...');
    let processingComplete = false;
    let attempts = 0;
    const maxAttempts = 30; // 5 minutes max

    while (!processingComplete && attempts < maxAttempts) {
      await sleep(10000); // Wait 10 seconds
      attempts++;

      try {
        const statusResponse = await axios.get(`${BASE_URL}/documents/${documentId}/processing-status`, {
          headers: { 'Authorization': AUTH_TOKEN }
        });
        const status = statusResponse.data.status;
        
        console.log(`   Attempt ${attempts}: Status = ${status}`);

        if (status === 'completed' || status === 'analyzed') {
          processingComplete = true;
          console.log('✅ Document processing completed');
        } else if (status === 'failed') {
          throw new Error('Document processing failed');
        }
      } catch (error) {
        console.log(`   Status check failed: ${error.message}`);
      }
    }

    if (!processingComplete) {
      throw new Error('Document processing timed out');
    }

    // Step 3: Check chat readiness
    console.log('\n🔍 Step 3: Checking document chat readiness...');
    const readinessResponse = await axios.get(`${BASE_URL}/chat/documents/${documentId}/chat-readiness`, {
      headers: { 'Authorization': AUTH_TOKEN }
    });
    
    console.log('✅ Chat readiness check completed');
    console.log(`   Ready: ${readinessResponse.data.ready}`);
    console.log(`   Status: ${readinessResponse.data.status}`);
    console.log(`   Message: ${readinessResponse.data.message}`);

    if (readinessResponse.data.details) {
      console.log(`   Content Length: ${readinessResponse.data.details.contentLength}`);
      console.log(`   Sections: ${readinessResponse.data.details.sectionsCount}`);
    }

    if (!readinessResponse.data.ready) {
      throw new Error(`Document not ready for chat: ${readinessResponse.data.message}`);
    }

    // Step 4: Create chat session
    console.log('\n💬 Step 4: Creating chat session...');
    const sessionResponse = await axios.post(`${BASE_URL}/chat/sessions`, {
      documentId: documentId,
      title: 'Test Chat Session'
    }, {
      headers: { 'Authorization': AUTH_TOKEN }
    });

    sessionId = sessionResponse.data.id;
    console.log('✅ Chat session created successfully');
    console.log(`   Session ID: ${sessionId}`);
    console.log(`   Title: ${sessionResponse.data.title}`);

    // Step 5: Send test messages
    console.log('\n📝 Step 5: Testing chat messages...');
    
    const testMessages = [
      "What is this document about?",
      "Who are the parties involved?",
      "What are the payment terms?",
      "Summarize the key obligations"
    ];

    for (let i = 0; i < testMessages.length; i++) {
      const message = testMessages[i];
      console.log(`\n   Sending message ${i + 1}: "${message}"`);

      try {
        const messageResponse = await axios.post(`${BASE_URL}/chat/messages`, {
          sessionId: sessionId,
          content: message
        }, {
          headers: { 'Authorization': AUTH_TOKEN },
          timeout: 60000 // 1 minute timeout for AI response
        });

        console.log('   ✅ Message sent and response received');
        console.log(`   Response length: ${messageResponse.data.content?.length || 0} characters`);
        
        // Show first 100 characters of response
        if (messageResponse.data.content) {
          const preview = messageResponse.data.content.substring(0, 100);
          console.log(`   Response preview: "${preview}${messageResponse.data.content.length > 100 ? '...' : ''}"`);
        }

        // Wait a bit between messages
        await sleep(2000);
      } catch (error) {
        console.log(`   ❌ Message failed: ${error.response?.data?.message || error.message}`);
      }
    }

    // Step 6: Test streaming chat
    console.log('\n🌊 Step 6: Testing streaming chat...');
    try {
      const streamResponse = await axios.post(`${BASE_URL}/chat/stream/messages`, {
        sessionId: sessionId,
        content: "Can you provide a detailed analysis of this contract?"
      }, {
        headers: { 'Authorization': AUTH_TOKEN },
        timeout: 60000,
        responseType: 'stream'
      });

      console.log('✅ Streaming chat initiated');
      console.log(`   Response status: ${streamResponse.status}`);
      console.log(`   Content type: ${streamResponse.headers['content-type']}`);
    } catch (error) {
      console.log(`❌ Streaming chat failed: ${error.response?.data?.message || error.message}`);
    }

    // Step 7: Get session history
    console.log('\n📚 Step 7: Retrieving session history...');
    const historyResponse = await axios.get(`${BASE_URL}/chat/sessions/${sessionId}`, {
      headers: { 'Authorization': AUTH_TOKEN }
    });
    
    console.log('✅ Session history retrieved');
    console.log(`   Total messages: ${historyResponse.data.messages?.length || 0}`);
    console.log(`   Session created: ${historyResponse.data.createdAt}`);

    console.log('\n🎉 Complete flow test successful!');
    console.log('\n📋 Test Summary:');
    console.log(`   ✅ Document uploaded: ${documentId}`);
    console.log(`   ✅ Document processed successfully`);
    console.log(`   ✅ Chat readiness validated`);
    console.log(`   ✅ Chat session created: ${sessionId}`);
    console.log(`   ✅ Messages sent and received`);
    console.log(`   ✅ Streaming chat tested`);
    console.log(`   ✅ Session history retrieved`);

  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
    if (error.response) {
      console.error('   Response status:', error.response.status);
      console.error('   Response data:', error.response.data);
    }
  } finally {
    // Cleanup
    try {
      if (sessionId) {
        console.log('\n🧹 Cleaning up: Deleting chat session...');
        await axios.delete(`${BASE_URL}/chat/sessions/${sessionId}`, {
          headers: { 'Authorization': AUTH_TOKEN }
        });
        console.log('✅ Chat session deleted');
      }
    } catch (error) {
      console.log('⚠️  Cleanup warning:', error.message);
    }

    try {
      if (documentId) {
        console.log('🧹 Cleaning up: Deleting document...');
        await axios.delete(`${BASE_URL}/documents/${documentId}`, {
          headers: { 'Authorization': AUTH_TOKEN }
        });
        console.log('✅ Document deleted');
      }
    } catch (error) {
      console.log('⚠️  Cleanup warning:', error.message);
    }

    // Remove test file
    try {
      fs.unlinkSync(testFilePath);
      console.log('✅ Test file cleaned up');
    } catch (error) {
      console.log('⚠️  File cleanup warning:', error.message);
    }
  }
}

// Check if server is running and start test
async function checkServerAndRunTest() {
  try {
    // Try to access documents endpoint with auth to check if server is running
    await axios.get(`${BASE_URL}/documents`, {
      headers: { 'Authorization': AUTH_TOKEN },
      timeout: 5000
    });
    console.log('🚀 Server is running, starting complete flow test...\n');
    await testCompleteDocumentChatFlow();
  } catch (error) {
    if (error.code === 'ECONNREFUSED') {
      console.log('⚠️  Server is not running. Please start the server first:');
      console.log('   npm run start:dev');
      console.log('\nThen run this test again:');
      console.log('   node test-complete-document-chat-flow.js');
    } else {
      console.log('🚀 Server is running, starting complete flow test...\n');
      await testCompleteDocumentChatFlow();
    }
  }
}

checkServerAndRunTest();
