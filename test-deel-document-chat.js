// Test with the Deel document from root folder
const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');
const readline = require('readline');
const path = require('path');

const BASE_URL = 'http://localhost:3000/api';
const AUTH_TOKEN = 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************.kNQXDoCw1m0dfwK6cPmZvDErewqaOAfj2Hrvdwv4yK8';

const DEEL_DOCUMENT_PATH = './deel-Osazee Agbonze - Turing remote work agreement - Google-contract-m6e4r22.pdf';

let documentId = null;
let sessionId = null;

// Create readline interface for interactive chat
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

async function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

async function uploadDeelDocument() {
  console.log('📤 Uploading Deel Remote Work Agreement...\n');

  // Check if the file exists
  if (!fs.existsSync(DEEL_DOCUMENT_PATH)) {
    throw new Error(`Deel document not found at: ${DEEL_DOCUMENT_PATH}`);
  }

  const fileStats = fs.statSync(DEEL_DOCUMENT_PATH);
  console.log(`📄 File found: ${path.basename(DEEL_DOCUMENT_PATH)}`);
  console.log(`📊 File size: ${(fileStats.size / 1024 / 1024).toFixed(2)} MB`);

  try {
    const formData = new FormData();
    formData.append('file', fs.createReadStream(DEEL_DOCUMENT_PATH));
    formData.append('title', 'Deel Remote Work Agreement - Osazee Agbonze');
    formData.append('author', 'Deel Legal Team');

    console.log('⬆️  Uploading to server...');
    const uploadResponse = await axios.post(`${BASE_URL}/documents/upload`, formData, {
      headers: {
        ...formData.getHeaders(),
        'Authorization': AUTH_TOKEN
      },
      timeout: 60000 // 1 minute timeout for large PDF
    });

    documentId = uploadResponse.data.document.id;
    console.log('✅ Document uploaded successfully!');
    console.log(`📄 Document ID: ${documentId}`);
    console.log(`📊 File size: ${fileStats.size} bytes`);
    console.log(`⏳ Status: ${uploadResponse.data.document.processingStatus}`);

    return documentId;
  } catch (error) {
    console.error('❌ Upload failed:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
    throw error;
  }
}

async function waitForProcessing(docId) {
  console.log('\n⏳ Waiting for PDF processing (this may take longer for PDFs)...');
  
  for (let attempt = 1; attempt <= 30; attempt++) { // Increased attempts for PDF
    try {
      const statusResponse = await axios.get(`${BASE_URL}/documents/${docId}/processing-status`, {
        headers: { 'Authorization': AUTH_TOKEN }
      });

      const status = statusResponse.data.status;
      const progress = statusResponse.data.progress || 0;
      
      process.stdout.write(`\r   Processing... Attempt ${attempt}/30, Status: ${status}, Progress: ${progress}%`);

      if (status === 'completed' || status === 'analyzed') {
        console.log('\n✅ Document processing completed!');
        return true;
      } else if (status === 'failed') {
        console.log('\n❌ Document processing failed!');
        if (statusResponse.data.error) {
          console.log(`Error: ${statusResponse.data.error}`);
        }
        return false;
      }

      await sleep(3000); // Wait 3 seconds between checks
    } catch (error) {
      console.log(`\n⚠️  Status check failed: ${error.message}`);
    }
  }

  console.log('\n⏰ Processing timeout - but let\'s check if it\'s ready for chat anyway...');
  return false;
}

async function checkChatReadiness(docId) {
  console.log('\n🔍 Checking if Deel document is ready for chat...');
  
  try {
    const readinessResponse = await axios.get(`${BASE_URL}/chat/documents/${docId}/chat-readiness`, {
      headers: { 'Authorization': AUTH_TOKEN }
    });

    console.log(`📋 Chat Readiness: ${readinessResponse.data.ready ? '✅ READY' : '❌ NOT READY'}`);
    console.log(`📝 Status: ${readinessResponse.data.status}`);
    console.log(`💬 Message: ${readinessResponse.data.message}`);

    if (readinessResponse.data.details) {
      const details = readinessResponse.data.details;
      console.log(`📊 Content Length: ${details.contentLength || 0} characters`);
      console.log(`📄 Document Status: ${details.documentStatus || 'unknown'}`);
      if (details.sectionsCount !== undefined) {
        console.log(`📑 Sections: ${details.sectionsCount}`);
      }
    }

    return readinessResponse.data.ready;
  } catch (error) {
    console.error('❌ Chat readiness check failed:', error.message);
    return false;
  }
}

async function createChatSession(docId) {
  console.log('\n💬 Creating chat session for Deel document...');
  
  try {
    const sessionResponse = await axios.post(`${BASE_URL}/chat/sessions`, {
      documentId: docId,
      title: 'Deel Remote Work Agreement Analysis'
    }, {
      headers: { 'Authorization': AUTH_TOKEN }
    });

    sessionId = sessionResponse.data.id;
    console.log('✅ Chat session created successfully!');
    console.log(`🆔 Session ID: ${sessionId}`);
    console.log(`📝 Title: ${sessionResponse.data.title}`);

    return sessionId;
  } catch (error) {
    console.error('❌ Failed to create chat session:', error.message);
    if (error.response) {
      console.error('Response:', error.response.data);
    }
    throw error;
  }
}

async function sendMessage(message) {
  try {
    console.log(`\n🤖 AI is analyzing the Deel contract...`);
    
    const messageResponse = await axios.post(`${BASE_URL}/chat/messages`, {
      sessionId: sessionId,
      content: message
    }, {
      headers: { 'Authorization': AUTH_TOKEN },
      timeout: 90000 // 90 seconds for complex contract analysis
    });

    const response = messageResponse.data.content;
    console.log(`\n🤖 AI Response:\n${response}\n`);
    
    return response;
  } catch (error) {
    console.error('❌ Failed to send message:', error.message);
    if (error.response) {
      console.error('Response:', error.response.data);
    }
    return null;
  }
}

function askQuestion(question) {
  return new Promise((resolve) => {
    rl.question(question, (answer) => {
      resolve(answer);
    });
  });
}

async function startInteractiveChat() {
  console.log('\n🎉 Welcome to Deel Contract Analysis Chat!');
  console.log('💡 You can now ask questions about the Deel Remote Work Agreement');
  console.log('💡 Type "exit" to end the chat session');
  console.log('💡 Type "help" for suggested questions about the contract\n');

  const suggestedQuestions = [
    "What is this Deel contract about?",
    "Who are the parties involved in this agreement?",
    "What are the key terms and conditions?",
    "What are the compensation and payment terms?",
    "What are the termination conditions?",
    "What intellectual property terms are included?",
    "What are the confidentiality requirements?",
    "What are the working arrangements and expectations?",
    "What benefits or perks are mentioned?",
    "What are the dispute resolution procedures?"
  ];

  while (true) {
    const userInput = await askQuestion('💬 Your question about the Deel contract: ');
    
    if (userInput.toLowerCase() === 'exit') {
      console.log('\n👋 Ending chat session...');
      break;
    }
    
    if (userInput.toLowerCase() === 'help') {
      console.log('\n💡 Suggested questions about the Deel contract:');
      suggestedQuestions.forEach((q, i) => {
        console.log(`   ${i + 1}. ${q}`);
      });
      console.log('');
      continue;
    }
    
    if (userInput.trim() === '') {
      console.log('⚠️  Please enter a question or type "exit" to quit.\n');
      continue;
    }

    await sendMessage(userInput);
  }
}

async function cleanup() {
  try {
    if (sessionId) {
      console.log('🧹 Cleaning up chat session...');
      await axios.delete(`${BASE_URL}/chat/sessions/${sessionId}`, {
        headers: { 'Authorization': AUTH_TOKEN }
      });
      console.log('✅ Chat session deleted');
    }

    if (documentId) {
      console.log('🧹 Cleaning up document...');
      await axios.delete(`${BASE_URL}/documents/${documentId}`, {
        headers: { 'Authorization': AUTH_TOKEN }
      });
      console.log('✅ Document deleted');
    }
  } catch (error) {
    console.log('⚠️  Cleanup warning:', error.message);
  }
}

async function main() {
  try {
    console.log('🚀 Starting Deel Document Upload and Chat Test\n');
    console.log('📋 This will upload and analyze the Deel Remote Work Agreement PDF\n');

    // Step 1: Upload Deel document
    await uploadDeelDocument();

    // Step 2: Wait for processing
    const processingSuccess = await waitForProcessing(documentId);

    // Step 3: Check chat readiness
    const isReady = await checkChatReadiness(documentId);

    if (!isReady) {
      console.log('❌ Deel document is not ready for chat. This might be due to:');
      console.log('   - PDF processing still in progress');
      console.log('   - Content extraction issues');
      console.log('   - Document format not supported');
      console.log('\nExiting...');
      return;
    }

    // Step 4: Create chat session
    await createChatSession(documentId);

    // Step 5: Start interactive chat
    await startInteractiveChat();

  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
  } finally {
    await cleanup();
    rl.close();
    console.log('\n✅ Deel document test completed!');
  }
}

// Handle Ctrl+C gracefully
process.on('SIGINT', async () => {
  console.log('\n\n🛑 Interrupted by user');
  await cleanup();
  rl.close();
  process.exit(0);
});

main();
